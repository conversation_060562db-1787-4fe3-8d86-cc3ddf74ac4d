import{I as s,J as e,K as t}from"./index-Blktg-U-.js";const n=async()=>await s(`${t}/capacity/getSolutionTypes`),r=async a=>await s(`${t}/capacity/getSolutionList`,a),i=async a=>await s(`${t}/capacity/getProject`,a),o=async a=>await s(`${t}/capacity/getResult`,a),u=async a=>await s(`${t}/capacity/getSolution`,a),y=async a=>await s(`${t}/config/getForecast`,a),g=async a=>await s(`${t}/config/getDevParams`,a),m=async()=>await s(`${t}/config/getH2AbsorbConfig`),l=async()=>await s(`${t}/config/getGridPrice`),p=async a=>await e(`${t}/capacity/submitTask`,a),w=async a=>await e(`${t}/capacity/saveSolution`,a),P=async a=>await e(`${t}/capacity/mdfCalcParams`,a),$=async a=>await e(`${t}/capacity/modifyProject`,a),d=async a=>await e(`${t}/config/addDevParams`,a),f=async a=>await e(`${t}/config/updateDevParams`,a),v=async a=>await e(`${t}/config/deleteDevParams`,a);export{n as a,r as b,o as c,p as d,u as e,y as f,i as g,g as h,l as i,m as j,$ as k,d as l,P as m,v as n,w as s,f as u};
