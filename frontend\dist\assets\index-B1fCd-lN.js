import{N as e,W as s,O as t}from"./index-C98MXVsn.js";const n=async()=>await e(`${t}/capacity/getSolutionTypes`),r=async a=>await e(`${t}/capacity/getSolutionList`,a),i=async a=>await e(`${t}/capacity/getProject`,a),o=async a=>await e(`${t}/capacity/getResult`,a),u=async a=>await e(`${t}/capacity/getSolution`,a),y=async a=>await e(`${t}/config/getForecast`,a),g=async a=>await e(`${t}/config/getDevParams`,a),d=async()=>await e(`${t}/config/getH2AbsorbConfig`),l=async a=>await s(`${t}/capacity/submitTask`,a),p=async a=>await s(`${t}/capacity/saveSolution`,a),w=async a=>await s(`${t}/capacity/mdfCalcParams`,a),P=async a=>await s(`${t}/capacity/modifyProject`,a),$=async a=>await s(`${t}/config/addDevParams`,a),m=async a=>await s(`${t}/config/updateDevParams`,a),f=async a=>await s(`${t}/config/deleteDevParams`,a),v=async a=>await s(`${t}/capacity/deleteSolution`,a),S=async a=>await e(`${t}/config/getZone`,a),D=async a=>await e(`${t}/config/getGridPrice`,a),G=async a=>await s(`${t}/config/updateGridPrice`,a),b=async a=>await s(`${t}/config/deleteGridPrice`,a);export{n as a,r as b,u as c,o as d,g as e,l as f,i as g,y as h,D as i,d as j,P as k,S as l,w as m,b as n,m as o,$ as p,f as q,v as r,p as s,G as u};
