export const t = () => {
  return [
    {
      title: '',
      dataIndex: '',
    },

    {
      title: '操作',
      dataIndex: 'action',
      key: 'action'
    },
  ]
}

export const pvColumns = () => {
  return [
    {
      title: '厂商',
      dataIndex: 'manufacturer',
      s_type: 'string',
      rules: [{ required: true, message: '请输入厂商' }],
      unit: ''
    },
    {
      title: '产品型号',
      dataIndex: 'model',
      s_type: 'string',
      rules: [{ required: true, message: '请输入产品型号' }],
      unit: ''
    },
    {
      title: '衰减率曲线',
      dataIndex: 'damp_curve',
      key: 'damp_curve',
      width: 200
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action'
    },
  ]
}

export const windColumns = () => {
  return [
    {
      title: '',
      dataIndex: '',
    },

    {
      title: '操作',
      dataIndex: 'action',
      key: 'action'
    },
  ]
}

export const batColumns = () => {
  return [
    {
      title: '厂商',
      dataIndex: 'manufacturer',
      s_type: 'string',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '产品型号',
      dataIndex: 'model',
      s_type: 'string',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '单机功率(MW)',
      dataIndex: 'single_power',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    // {
    //   title: '衰减曲线',
    //   dataIndex: 'damp_curve',
    //   s_type: 'number',
    //   rules: [{ required: false }],
    //   unit: ''
    // },
    {
      title: '充电效率',
      dataIndex: 'charge_efficiency',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '放电效率',
      dataIndex: 'discharge_efficiency',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '充放电倍率',
      dataIndex: 'c_rate',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    // {
    //   title: '储能占比',
    //   dataIndex: 'radio',
    //   s_type: 'number',
    //   rules: [{ required: false }],
    //   unit: ''
    // },
    {
      title: '初始SOC',
      dataIndex: 'init_soc',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: 'SOC下限',
      dataIndex: 'min_soc',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: 'SOC上限',
      dataIndex: 'max_soc',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    // {
    //   title: '置信度',
    //   dataIndex: 'confidence',
    //   s_type: 'number',
    //   rules: [{ required: false }],
    //   unit: ''
    // },
    {
      title: '寿命(年)',
      dataIndex: 'life_cycle',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action'
    },
  ];
}

export const alkColumns = () => {
  return [
    {
      title: '厂商',
      dataIndex: 'manufacturer',
      s_type: 'string',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '产品型号',
      dataIndex: 'model',
      s_type: 'string',
      rules: [{ required: false }],
      unit: '',
      width: '80px'
    },
    {
      title: '类型',
      dataIndex: 'ele_type',
      key: 'ele_type',
      s_type: 'select',
      rules: [{ required: false }],
      unit: '',
      options: [
        { label: 'ALK', value: 1, color: 'blue' },
        { label: 'PEM', value: 2, color: 'green'  },
      ]
    },
    {
      title: '容量(Nm³/h)',
      dataIndex: 'capacity',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '能耗(kwh/Nm³)',
      dataIndex: 'power_consumption',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '额定功率(MW)',
      dataIndex: 'pe',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '最低负载率',
      dataIndex: 'lower_load_rate',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '最高负载率',
      dataIndex: 'upper_load_rate',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    // {
    //   title: '衰减曲线',
    //   dataIndex: 'damp_curve',
    //   s_type: 'number',
    //   rules: [{ required: false }],
    //   unit: ''
    // },
    {
      title: '辅助系统能耗(kwh/Nm³)',
      dataIndex: 'assist_consumption',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '系统价格(元/套)',
      dataIndex: 'price',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '制氢电源效率',
      dataIndex: 'power_supply_efficiency',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: '100px'
    },
  ]
}

export const alkStoreColumns = () => {
  return [
    {
      title: '厂商',
      dataIndex: 'manufacturer',
      s_type: 'string',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '产品型号',
      dataIndex: 'model',
      s_type: 'string',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '体积(m³)',
      dataIndex: 'volume',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '最小运行压力(Mpa)',
      dataIndex: 'min_pressure',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '最大运行压力(Mpa)',
      dataIndex: 'max_pressure',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '价格(元)',
      dataIndex: 'price',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '占地面积(㎡)',
      dataIndex: 'area',
      s_type: 'number',
      rules: [{ required: false }],
      unit: ''
    },    
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action'
    },
  ]
}




