import{A as x}from"./index-C98MXVsn.js";import{u as m,w as b}from"./xlsx-D5JNrnKm.js";const E=async(e,o)=>{const t=document.createElement("a");t.href=e,t.download=o,t.style.display="none",document.body.appendChild(t),t.click(),t.remove()},_=e=>x(e).format("YYYY-MM-DD HH:mm"),g=async e=>{const t=new TextEncoder().encode(e);let s="";for(let a=0;a<t.length;a++)s+=String.fromCharCode(t[a]);return btoa(s)},Y=(e,o)=>e?e.find(t=>t.value===o):o,v=(e,o,t,s,a="经济分析详情数据.xlsx")=>{const i=m.book_new();e.forEach(c=>{const l=t(c),d=s(c);if(d&&d.length>0){const u=l.map(n=>n.title),p=d.map(n=>l.map(y=>{const r=n[y.dataIndex];return r==="--"||r===null||r===void 0?"":r})),f=[u,...p],h=m.aoa_to_sheet(f),w=l.map(n=>({wch:Math.max(n.title.length,15)}));h["!cols"]=w,m.book_append_sheet(i,h,c.tab)}}),b(i,a)};export{E as d,v as e,_ as f,Y as g,g as s};
