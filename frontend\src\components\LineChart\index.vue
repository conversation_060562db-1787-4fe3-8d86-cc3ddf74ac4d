<template>
  <div class="line_chart" ref="container"></div>
</template>
  
<script setup>
import { ref, onMounted,onUpdated, reactive } from 'vue';
import * as echarts from 'echarts';
import { throttle } from 'lodash'

const container = ref()
const zoomRange = ref([40, 50])
const props = defineProps(['data'])
let myChart;

const pieces = []
const statusColor = ['#5cc', '#c5c', '#5c5']
for (let i = 0; i < 24; i++) {
  pieces.push({ gt: i, lt: i + 1, color: statusColor[i % 3]})
}
// 绘制图表
let option = {
  title: {
    text: ''
  },
  xAxis: {
    type: 'category',
    boundaryGap: false
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['下发', '实时']
  },
  yAxis: {
    type: 'value',
    boundaryGap: [0, '10%'],
  },
  visualMap: {
    type: 'piecewise',
    show: false,
    dimension: 0,
    seriesIndex: 0,
    pieces: pieces
  },
  series: [
  ],
  
}
const resize = () => {
  myChart.resize()
}
//  
onMounted(() => {
  // 基于准备好的dom，初始化echarts实例
  // myChart = echarts.init(document.querySelector(`#${props.data.id}`))
  // console.log('update1:', props.data, option)

  myChart = echarts.init(container.value)
  myChart.setOption({
    ...option,
    ...props.data
  });
  const resizeThrottle = throttle(() => resize(), 300);

  window.addEventListener('resize', resizeThrottle);
  
  myChart.on('datazoom', (params) => {
    const config = params?.batch?.[0] || params
    zoomRange.value = [config.start, config.end]
  })
  setTimeout(() => {
    resize()
  }, 2000)
  
})

onUpdated(() => {
  myChart.clear()

  // console.log('update2:', props.data, option)
  option = {
    ...option,
    ...props.data
  }
  // myChart.setOption(option, true);
  myChart.clear()
  if (option.dataZoom) {
    option.dataZoom[0].start = zoomRange?.value?.[0]
    option.dataZoom[0].end = zoomRange?.value?.[1]
  }
  myChart.setOption(option, true);
 
})

defineExpose({
  resize: resize
})
</script>
  
<style scoped lang="less">
.line_chart {
  padding: 0 10px;
  width: 100%;
  // width: 400px;
  // height: 200px;
  height: 100%;
  // margin-bottom: 5px;
}

</style>
  