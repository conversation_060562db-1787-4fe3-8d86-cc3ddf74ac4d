<template>
  <a-layout class="dark-side-layout">
    <!-- 左侧深色导航 -->
    <a-layout-sider
      @collapse="toggleCollapsed"
      width="200"
      collapsible
      theme="dark"
      :style="{
        overflow: 'auto',
        height: '100vh',
        background: siderBgColor,
        position: 'fixed',
        left: 0,
        top: 0,
        bottom: 0,
        transition: 'all 0.3s ease'
      }"
      :collapsed="baseStore.menuCollapsed"
    >
      <!-- Logo区域 -->
      <div class="logo-section" :style="{ background: siderBgColor }">
        <div class="logo-content" @click="router.push({name:'home'})">
          <DropboxOutlined class="logo-icon" />
          <div v-if="!baseStore.menuCollapsed" class="logo-text">{{ baseConfig.title }}</div>
        </div>
      </div>
      
      <!-- Debug info (remove in production) -->
      <div style="padding: 10px; font-size: 12px; background: rgba(255,255,255,0.1); margin-bottom: 10px;" v-if="false">
        <div style="color: #fff;">Route: {{ route.name }}</div>
        <div style="color: #fff;">Meta Key: {{ route.meta?.key }}</div>
        <div style="color: #fff;">Selected Keys: {{ state.selectedKeys }}</div>
      </div>
      
      <a-menu
        mode="inline"
        theme="dark"
        @click="navTo"
        v-model:openKeys="state.openKeys"
        v-model:selectedKeys="state.selectedKeys"
        :items="MenuItems()"
        :style="{ 
          background: siderBgColor,
          border: 'none'
        }"
      >
      </a-menu>
    </a-layout-sider>
    
    <!-- 右侧内容区 -->
    <a-layout :style="{
      marginLeft: baseStore.menuCollapsed ? '80px' : '200px',
      transition: 'margin-left 0.3s ease'
    }">
      <!-- 右侧顶部Header -->
      <a-layout-header 
        :style="{ 
          padding: 0, 
          height: 'auto', 
          lineHeight: 'normal',
          background: '#fff',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          zIndex: 1
        }"
      >
        <Header :config="{ hideTitle: true }" />
      </a-layout-header>
      
      <!-- 右侧内容区域 -->
      <a-layout-content 
        :style="{ 
          margin: 0, 
          height: 'calc(100vh - 64px)', 
          overflow: 'auto',
          background: '#f5f5f5'
        }"
      >
        <RouterView />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script lang="ts" setup>
import { reactive, onMounted, watch, computed } from 'vue';
import { RouterView } from 'vue-router';
import MenuItems from '@/components/Menu/menuConfig'
import Header from '@/components/Header/index.vue'
import { useBaseStore } from '@/stores/base'
import { useRouter, useRoute } from 'vue-router';
import { DropboxOutlined } from '@ant-design/icons-vue'
import { baseConfig } from '@/config/baseConfig'

const baseStore = useBaseStore()
const router = useRouter();
const route = useRoute()

// 基于主色调生成深色背景色
const siderBgColor = computed(() => {
  // 根据主色调生成对应的深色背景
  const baseColor = baseConfig.baseColor;
  if (baseColor === '#084B9C') {
    // 蓝色主题 - 使用深蓝色背景
    return '#001529';
  } else if (baseColor === '#15a675') {
    // 绿色主题 - 使用深绿色背景
    return '#0f1419';
  } else {
    // 默认使用Ant Design深色主题色
    return '#001529';
  }
})

interface StateType {
  selectedKeys: string[];
  openKeys: string[];
  preOpenKeys: string[];
}

const state = reactive<StateType>({
  selectedKeys: [],
  openKeys: [],
  preOpenKeys: [],
});

const toggleCollapsed = () => {
  console.log('Toggle collapsed')
  baseStore.collapseMenu()
  state.openKeys = baseStore.menuCollapsed ? [] : state.preOpenKeys;
};

const navTo = (route: { key: string }) => {
  console.log('Navigate to:', route)
  router.push({
    name: route.key,
  });
};

const setSelectedMenu = (key: string) => {
  if (!key) return

  console.log('Setting selected menu:', key)
  state.selectedKeys = [key]

  // 设置展开的菜单项
  const menuItems = MenuItems()
  for (const item of menuItems) {
    if ((item as any).children) {
      for (const child of (item as any).children) {
        if (child.key === key) {
          if (!state.openKeys.includes(item.key)) {
            state.openKeys.push(item.key)
            state.preOpenKeys.push(item.key)
          }
          break
        }
      }
    }
  }
}

const debugRoute = (route: any) => {
  console.log('Debug route info:', {
    name: route.name,
    path: route.path,
    meta: route.meta,
    params: route.params,
    query: route.query
  })
}

watch(route, (newRoute) => {
  console.log('Route changed:', {
    name: newRoute.name,
    path: newRoute.path,
    metaKey: newRoute.meta?.key
  })
  debugRoute(newRoute)
  setSelectedMenu(newRoute.meta?.key as string)
}, { deep: true, immediate: true })

onMounted(() => {
  console.log('DarkSideMenuLayout mounted, current route:', {
    name: route.name,
    path: route.path,
    metaKey: route.meta?.key
  })
  debugRoute(route)
  setSelectedMenu(route.meta?.key as string)
  
  if (baseStore.menuCollapsed) {
    state.openKeys = []
  }
})
</script>

<style scoped lang="less">
@import '@/style/base.less';

.dark-side-layout {
  min-height: 100vh;
}

// 侧边栏过渡动画
:deep(.ant-layout-sider) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

  .ant-layout-sider-children {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.logo-section {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .logo-content {
    display: flex;
    align-items: center;
    color: #fff;
    font-size: 18px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      color: @primaryColor;
    }

    .logo-icon {
      font-size: 24px;
      color: @primaryColor;
      margin-right: 8px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .logo-text {
      white-space: nowrap;
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      opacity: 1;
    }
  }
}

// 自定义深色菜单样式
:deep(.ant-menu-dark) {
  background: transparent;

  .ant-menu-item {
    &:hover {
      background-color: rgba(255, 255, 255, 0.1) !important;
    }

    &.ant-menu-item-selected {
      background-color: @primaryColor !important;

      &::after {
        display: none;
      }
    }
  }

  .ant-menu-submenu {
    &:hover > .ant-menu-submenu-title {
      background-color: rgba(255, 255, 255, 0.1) !important;
    }
  }

  .ant-menu-submenu-selected > .ant-menu-submenu-title {
    color: @primaryColor !important;
  }
}

// 折叠状态下的样式调整
:deep(.ant-layout-sider-collapsed) {
  .logo-section {
    .logo-content {
      justify-content: center;

      .logo-icon {
        margin-right: 0 !important;
      }

      .logo-text {
        opacity: 0;
        width: 0;
        margin-left: 0;
      }
    }
  }
}

// 菜单项过渡动画
:deep(.ant-menu) {
  .ant-menu-item,
  .ant-menu-submenu {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

    .ant-menu-title-content {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
}
</style>
