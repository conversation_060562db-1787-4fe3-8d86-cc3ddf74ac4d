import request from './request';
export interface Res<T> {
  code: number;
  data: T;
  msg: string;
}

export const urlPrefix = '/api/v1/cecp';

export async function get(url: string, params?: any) {
  return request({
    method: 'get',
    url,
    params
  }) 
}

export async function post(url: string, data?: any, headers?: any) {
  return request({
    method: 'post',
    url,
    data,
    headers
  })
}

export async function del(url: string, data?: any, headers?: any) {
  return request({
    method: 'delete',
    url,
    data,
    headers
  })
}

