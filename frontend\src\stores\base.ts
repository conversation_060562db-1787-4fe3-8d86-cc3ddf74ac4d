// stores/counter.js
import { defineStore } from 'pinia'
import { setLocale } from '@/lang'

interface BaseStore {
  menuCollapsed: boolean;
  mainLayoutStyle: any;
  headerStyle: any;
  navStyle: any;
  lang: string;
}
export const useBaseStore = defineStore('base', {
  state: (): BaseStore => {
    return {
      menuCollapsed: localStorage.getItem('menuCollapsed') === 'true' ? true : false,
      mainLayoutStyle: {},
      lang: localStorage.getItem('lang') || 'zh_CN'
    }
  },
  // 也可以这样定义
  // state: () => ({ count: 0 })
  actions: {
    collapseMenu() {
      this.menuCollapsed = !this.menuCollapsed
      localStorage.setItem('menuCollapsed', this.menuCollapsed.toString())
    },
    setMainLayoutStyle(styleObj: any) {
      this.mainLayoutStyle = styleObj
    },
    setHeaderStyle(styleObj: any) {
      this.headerStyle = styleObj
    },
    setNavStyle(styleObj: any) {
      this.navStyle = styleObj
    },
    setLang(lang: string) {
      this.lang = lang
      setLocale(lang)
      localStorage.setItem('lang', lang)
    }
  },
})