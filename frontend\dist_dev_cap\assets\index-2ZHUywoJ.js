import{i as O,l as W}from"./lodash-DsxhgM2J.js";import{_ as $,r as w,o as A,a as Z,b as l,c as h,u as F,d as k,e,f as c,w as a,g as P,t as I,h as S,F as U,i as q,j as m,k as B,l as T,p as G,m as J}from"./index-Blktg-U-.js";import{g as M,a as H}from"./index-BbGSYVSM.js";const K="/assets/step1-BAZEOJx6.png",Q="/assets/step2-DSLmiWlE.png",X="/assets/step3-CagpBwa6.png",Y={__name:"index",props:["data"],setup(u,{expose:b}){const t=w(),r=w([40,50]),y=u;let i,n={grid:{top:"5%",bottom:"5%",left:"3%",right:"3%",containLabel:!0},xAxis:{type:"category",data:["风光_制氢","风光_网_制氢","风光_储能_制氢","风光_网_储能_制氢","风光_制氢_储氢","风光_网_制氢_储氢","风光_储能_制氢_储氢","风光_网_储能_制氢_储氢"],axisLabel:{rotate:60,fontSize:11,marginRight:0,color:"#333"}},yAxis:{type:"value",boundaryGap:!1},series:[{data:[120,200,150,80,70,110,130,200],type:"bar",barWidth:"50%"}]};const g=()=>{i.resize()};return A(()=>{i=O(t.value),i.setOption({...n,...y.data});const s=W.throttle(()=>g(),300);window.addEventListener("resize",s),i.on("datazoom",o=>{var v;const d=((v=o==null?void 0:o.batch)==null?void 0:v[0])||o;r.value=[d.start,d.end]}),setTimeout(()=>{g()},2e3)}),Z(()=>{i.clear(),n={...n,...y.data},i.clear(),n.dataZoom[0].start=r.value[0],n.dataZoom[0].end=r.value[1],i.setOption(n,!0)}),b({resize:g}),(s,o)=>(l(),h("div",{class:"line_chart",ref_key:"container",ref:t},null,512))}},ee=$(Y,[["__scopeId","data-v-4372e27f"]]),p=u=>(G("data-v-2932e333"),u=u(),J(),u),te={class:"body_wrap"},se={class:"part_wrap"},ae=p(()=>e("div",{class:"title1"}," 首页 ",-1)),ie={key:0,class:"content_wrap"},oe={class:"box_wrap box_recent"},de=p(()=>e("div",{class:"b_title"},"近期测算项目",-1)),_e={class:"b_body"},ne={class:"b_name"},ce={class:"b_k_v_item"},le=p(()=>e("div",{class:"key"},"客户名称：",-1)),re={class:"val"},ve={class:"b_k_v_item"},pe=p(()=>e("div",{class:"key"},"项目背景：",-1)),ue={class:"val"},me={class:"see_detail"},he={class:"box_wrap box_mine"},be=p(()=>e("div",{class:"b_title"},"我的项目",-1)),ye={class:"b_body"},ge={key:0},fe={key:1},ke={class:"see_detail"},we={class:"box_wrap box_stat"},xe=p(()=>e("div",{class:"b_title"},"项目统计",-1)),ze={class:"b_body"},Ce=p(()=>e("span",null," 暂无数据 ",-1)),Ie=P('<div class="part_wrap" data-v-2932e333><div class="title1" data-v-2932e333> 功能指引 </div><div class="step_wrap" data-v-2932e333><div class="s_w_title" data-v-2932e333>容量测算 </div><div class="step_item_wrap" data-v-2932e333><div class="step_item" data-v-2932e333><div class="number_wrap" data-v-2932e333>01</div><div class="line_wrap" data-v-2932e333></div></div><div class="step_item" data-v-2932e333><div class="number_wrap" data-v-2932e333>02</div><div class="line_wrap" data-v-2932e333></div></div><div class="step_item" data-v-2932e333><div class="number_wrap" data-v-2932e333>03</div><div class="line_wrap" data-v-2932e333></div></div></div><div class="step_content_wrap" data-v-2932e333><div class="s_c_item" data-v-2932e333><div class="s_c_title" data-v-2932e333>新建测算项目</div><div class="s_c_desc" data-v-2932e333>在项目测算页面，通过新建项目操作可以创建新项目，或基于项目创建新方案</div><img class="s_c_img" src="'+K+'" height="80%" data-v-2932e333></div><div class="s_c_item" data-v-2932e333><div class="s_c_title" data-v-2932e333>编辑项目参数</div><div class="s_c_desc" data-v-2932e333>在创建页面，输入项目信息，选择求解算法以及场景，配置相关参数</div><img class="s_c_img" src="'+Q+'" height="80%" data-v-2932e333></div><div class="s_c_item" data-v-2932e333><div class="s_c_title" data-v-2932e333>获取测算方案</div><div class="s_c_desc" data-v-2932e333>测算完成后，方案结果页面展示绿电、电网、制氢、储氢容量等信息，以及功率等曲线信息，并支持修改配置结果再次测算</div><img class="s_c_img" src="'+X+'" height="80%" data-v-2932e333></div></div></div></div>',1),Se={__name:"index",setup(u){const b=F(),t=w([]),r=w(!1),y=w({}),i=[{title:"方案名称",dataIndex:"name",key:"name"},{title:"客户名称",dataIndex:"customer",key:"customer"}],n=async()=>{r.value=!0;const{code:g,data:{total:s,result:o},msg:d}=await M({pageNumber:0,pageSize:5}),{data:v}=await H(),x=[];for(let z in v)x[parseInt(z)-1]=v[z].number;y.value={series:[{data:x,type:"bar",barWidth:"50%"}]},t.value=o,r.value=!1,console.log("r:",t)};return A(()=>{n()}),(g,s)=>{const o=k("a-tag"),d=k("a-button"),v=k("a-table"),x=k("a-empty"),z=k("a-spin");return l(),h("div",te,[e("div",se,[ae,c(z,{spinning:r.value},{default:a(()=>{var D,j,L,N,E;return[(D=t.value)!=null&&D.length?(l(),h("div",ie,[e("div",oe,[de,e("div",_e,[e("div",ne,I((j=t.value[0])==null?void 0:j.name),1),e("div",ce,[le,e("div",re,I((L=t.value[0])==null?void 0:L.customer),1)]),e("div",ve,[pe,e("div",ue,I((N=t.value[0])==null?void 0:N.desc),1)])]),e("div",me,[e("a",{type:"link",href:"void:0",onClick:s[0]||(s[0]=f=>{var C,_,V,R;return S(b).push({name:"projectDetail",query:{projectId:(C=t.value[0])==null?void 0:C.id,type:"list",solutionId:(R=(V=(_=t.value[0])==null?void 0:_.solutions)==null?void 0:V[0])==null?void 0:R.id}})})},"查看详情>")])]),e("div",he,[be,e("div",ye,[c(v,{columns:i,"data-source":t.value.slice(0,5),pagination:!1,size:"small"},{bodyCell:a(({column:f,record:C})=>[f.key==="tags"?(l(),h("span",ge,[(l(!0),h(U,null,q(C.tags,_=>(l(),T(o,{key:_,color:_==="loser"?"volcano":(_==null?void 0:_.length)>5?"geekblue":"green"},{default:a(()=>[m(I(_.toUpperCase()),1)]),_:2},1032,["color"]))),128))])):f.key==="action"?(l(),h("span",fe,[c(d,{type:"link",size:"small"},{default:a(()=>[m("详情")]),_:1}),c(d,{type:"link",size:"small"},{default:a(()=>[m("暂停/启动")]),_:1}),c(d,{type:"link",size:"small"},{default:a(()=>[m("删除")]),_:1}),c(d,{type:"link",size:"small"},{default:a(()=>[m("配置")]),_:1})])):B("",!0)]),_:1},8,["data-source"])]),e("div",ke,[e("a",{type:"link",href:"void:0",onClick:s[1]||(s[1]=f=>S(b).push({name:"projectList"}))},"查看详情>")])]),e("div",we,[xe,e("div",ze,[c(ee,{data:y.value},null,8,["data"])])])])):B("",!0),(E=t.value)!=null&&E.length?B("",!0):(l(),T(x,{key:1},{description:a(()=>[Ce]),default:a(()=>[c(d,{type:"primary",onClick:s[2]||(s[2]=f=>S(b).push({name:"createProject"}))},{default:a(()=>[m("创建新项目")]),_:1})]),_:1}))]}),_:1},8,["spinning"])]),Ie])}}},Le=$(Se,[["__scopeId","data-v-2932e333"]]);export{Le as default};
