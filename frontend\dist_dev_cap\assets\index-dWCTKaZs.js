import{k as oe,g as le}from"./index-BbGSYVSM.js";import{_ as se,u as ne,r as u,o as re,d as _,b as d,c as h,e as o,f as r,w as i,h as v,A as O,j as w,k as g,l as I,F as N,t as S,i as H,p as ie,m as ce}from"./index-Blktg-U-.js";import{l as ue}from"./lodash-DsxhgM2J.js";/* empty css                                                              */import{f as de,g as M}from"./index-CB8EjQs8.js";const pe=()=>[{title:"项目名称",dataIndex:"name",width:"150px"},{title:"客户名称",dataIndex:"customer",width:"100px"},{title:"项目背景",dataIndex:"desc",width:"140px"},{title:"项目操作",dataIndex:"action",key:"action",width:"150px"},{title:"方案",dataIndex:"",key:"solution",width:"65%"}],_e=()=>[{title:"名称",dataIndex:"name",key:"name",width:"100px"},{title:"场景",dataIndex:"topology",key:"topology",width:"200px"},{title:"求解目标",dataIndex:"targetExpr",key:"targetExpr"},{title:"创建时间",dataIndex:"createTime",key:"createTime"},{title:"状态",dataIndex:"status",key:"status",width:"100px"},{title:"操作",dataIndex:"action",key:"action",width:"330px"}],K=()=>[{label:"运行中",value:1,color:"processing"},{label:"成功",value:2,color:"success"},{label:"失败",value:3,color:"error"}],me=()=>["LCOH","投资成本最低","弃电率最低","产氢量最大"],ve=()=>["光伏","风机","电网","储能","制氢","储氢"],R=x=>(ie("data-v-ba46f3a9"),x=x(),ce(),x),fe={class:"body_wrap"},ge={class:"p_wrap"},he=R(()=>o("div",{class:"title_wrap"},null,-1)),be={class:"content_wrap",id:"content_wrap"},ye={class:"part_wrap"},ke={class:"p_title"},we=R(()=>o("div",null,"容量测算",-1)),xe={class:"btn_wrap"},Ce={class:"table_wrap"},Ie={key:0,class:"t_btn_wrap"},Se=["onClick"],Pe=["onClick"],je={key:0,class:"t_btn_wrap"},Ee=["onClick"],Te=["onClick"],ze=R(()=>o("a",{href:"void:0",class:"a_item",disabled:""},"经济分析",-1)),Ne={__name:"index",setup(x){const P=ne(),j=u(!1),C=u(!1),D=u(),L=u([]),m=u({}),$=u({}),E=u(!1),q=u(!1),A=u(0),n=u({operator:"",pageSize:10,pageNumber:1,searchWord:""}),G=t=>{const e=[],s=me();for(let a=0,c=s.length;a<c;a++)t[a]!==0&&e.push({label:s[a],value:t[a]});return e},J=t=>{const e=[],s=ve();for(let a=0,c=s.length;a<c;a++)t[a]&&e.push({label:s[a]});return e},Q=()=>{P.push({name:"createProject"})},B=(t,e)=>{console.log("ceateSolution:",t,e);const s={projectId:t.id};(e==null?void 0:e.id)!==void 0&&(s.solutionId=e.id),P.push({name:"createProject",query:s})},X=t=>{C.value=!0,$.value=t,m.value=ue.cloneDeep(t)},Y=async()=>{const t=await D.value.validateFields();E.value=!0;const{code:e,msg:s}=await oe({id:$.value.id,...t});E.value=!1,e===0?(O.success("修改成功"),U()):O.error(s),C.value=!1},T=async(t=!0)=>{var b;j.value=t;const e={pageSize:n.value.pageSize,pageNumber:n.value.pageNumber};n.value.searchWord&&(e.search=n.value.searchWord),(b=n.value.operator)!=null&&b.trim()&&(e.operator=n.value.operator);const{msg:s,data:a,code:c}=await le(e);console.log("code:",c,a),j.value=!1,c===0&&(L.value=a.result,A.value=a.total)},Z=t=>{console.log("page:",t),n.value.pageNumber=t.current,n.value.pageSize=t.pageSize,T()},U=async()=>{T()};return re(()=>{U(),setTimeout(()=>{q.value=!0},5e3)}),(t,e)=>{const s=_("a-button"),a=_("a-input-search"),c=_("a-tag"),b=_("a-table"),V=_("a-input"),z=_("a-form-item"),ee=_("a-textarea"),te=_("a-form"),ae=_("a-modal");return d(),h("div",fe,[o("div",ge,[he,o("div",be,[o("div",ye,[o("div",ke,[we,o("div",xe,[r(s,{class:"btn_item",size2:"small",type:"primary",onClick:Q},{default:i(()=>[w("新建项目")]),_:1})])]),o("div",Ce,[o("div",null,[r(a,{value:n.value.searchWord,"onUpdate:value":e[0]||(e[0]=l=>n.value.searchWord=l),placeholder:"请输入项目名称或客户名称",style:{width:"230px","margin-bottom":"15px"},onSearch:T},null,8,["value"])]),r(b,{borderd:"",class:"outer_table",loading:j.value,columns:v(pe)(),rowKey:"id","data-source":L.value,defaultExpandAllRows:q.value,onChange:Z,pagination:{pageSize:n.value.pageSize,total:A.value,hideOnSinglePage:!1,showTotal:l=>`总页数 ${l}`}},{bodyCell:i(({column:l,record:y})=>[l.key==="action"?(d(),h("div",Ie,[o("a",{href:"void:0",class:"a_item",onClick:f=>B(y)},"新建方案",8,Se),o("a",{href:"void:0",class:"a_item",onClick:f=>X(y)},"修改项目",8,Pe)])):g("",!0),l.key==="solution"?(d(),I(b,{key:1,class:"inner_table",showHeader:!0,columns:v(_e)(),"data-source":y.solutions,pagination:!1,size:"small",defaultExpandAllRows:!0},{bodyCell:i(({column:f,text:k,record:W})=>{var F;return[f.key==="action"?(d(),h("div",je,[o("a",{href:"void:0",class:"a_item",onClick:p=>v(P).push({name:"projectDetail",query:{projectId:y.id,solutionId:W.id,type:"list"}})},"查看",8,Ee),o("a",{href:"void:0",class:"a_item",onClick:p=>B(y,W)},"修改方案",8,Te),ze])):g("",!0),f.key==="createTime"?(d(),h(N,{key:1},[w(S(v(de)(k)),1)],64)):g("",!0),f.key==="status"?(d(),I(c,{key:2,color:(F=v(M)(v(K)(),k))==null?void 0:F.color},{default:i(()=>{var p;return[w(S((p=v(M)(v(K)(),k))==null?void 0:p.label),1)]}),_:2},1032,["color"])):g("",!0),f.key==="topology"?(d(!0),h(N,{key:3},H(J(k),p=>(d(),I(c,null,{default:i(()=>[w(S(p.label),1)]),_:2},1024))),256)):g("",!0),f.key==="targetExpr"?(d(!0),h(N,{key:4},H(G(k),p=>(d(),I(c,null,{default:i(()=>[w(S(`${p.label}: ${p.value*100}%`),1)]),_:2},1024))),256)):g("",!0)]}),_:2},1032,["columns","data-source"])):g("",!0)]),_:1},8,["loading","columns","data-source","defaultExpandAllRows","pagination"])])])])]),r(ae,{open:C.value,"onUpdate:open":e[4]||(e[4]=l=>C.value=l),title:"修改项目",onOk:Y,"confirm-loading":E.value},{default:i(()=>[o("div",null,[r(te,{labelAlign:"left2",ref_key:"formRef",ref:D,model:m.value,name:"basic","label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:i(()=>[r(z,{label:"项目名称",name:"name",rules:[{required:!0,message:"Please input!"}]},{default:i(()=>[r(V,{value:m.value.name,"onUpdate:value":e[1]||(e[1]=l=>m.value.name=l)},null,8,["value"])]),_:1}),r(z,{label:"客户名称",name:"customer",rules:[{required:!0,message:"Please input!"}]},{default:i(()=>[r(V,{value:m.value.customer,"onUpdate:value":e[2]||(e[2]=l=>m.value.customer=l)},null,8,["value"])]),_:1}),r(z,{label:"项目背景",name:"desc",rules:[{required:!0,message:"Please input!"}]},{default:i(()=>[r(ee,{value:m.value.desc,"onUpdate:value":e[3]||(e[3]=l=>m.value.desc=l)},null,8,["value"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["open","confirm-loading"])])}}},Ae=se(Ne,[["__scopeId","data-v-ba46f3a9"]]);export{Ae as default};
