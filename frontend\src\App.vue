<script setup lang="ts">
import { RouterView } from 'vue-router'
import { computed, watch } from 'vue'

import zhCN from 'ant-design-vue/es/locale/zh_CN';
import enUS from 'ant-design-vue/es/locale/en_US';
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/en';
import { baseConfig} from '@/config/baseConfig'
import { useBaseStore } from '@/stores/base'
import { useI18n } from 'vue-i18n'
import dayjs from 'dayjs'

const baseStore = useBaseStore()
const { locale } = useI18n()

// 根据当前语言设置 ant-design-vue 的语言包
const antdLocale = computed(() => {
  return baseStore.lang === 'zh_CN' ? zhCN : enUS
})

// 监听语言变化，设置 dayjs 语言
watch(() => baseStore.lang, (newLang) => {
  if (newLang === 'zh_CN') {
    dayjs.locale('zh-cn')
  } else {
    dayjs.locale('en')
  }
}, { immediate: true })
</script>

<template>
  <a-config-provider
    :locale="antdLocale"
    :theme="{
      token: {
        colorPrimary: baseConfig.baseColor
      }
    }">
    <RouterView />
  </a-config-provider>
</template>

<style scoped lang="less">
</style>
