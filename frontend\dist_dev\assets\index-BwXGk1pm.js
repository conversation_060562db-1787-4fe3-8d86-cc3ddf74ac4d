import{c as d,A as re,d as te,u as T,a as ne,r as k,b as se,e as oe,f as pe,o as de,g as _,h,i as t,w as n,j as p,m as E,k as s,t as i,I as me,F as C,l as U,n as O,p as m,q as L,_ as ie}from"./index-qMHOHHUH.js";import{s as ce,g as fe}from"./index-BB0yVVhp.js";var be={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"};const ve=be;function B(e){for(var u=1;u<arguments.length;u++){var y=arguments[u]!=null?Object(arguments[u]):{},f=Object.keys(y);typeof Object.getOwnPropertySymbols=="function"&&(f=f.concat(Object.getOwnPropertySymbols(y).filter(function(F){return Object.getOwnPropertyDescriptor(y,F).enumerable}))),f.forEach(function(F){_e(e,F,y[F])})}return e}function _e(e,u,y){return u in e?Object.defineProperty(e,u,{value:y,enumerable:!0,configurable:!0,writable:!0}):e[u]=y,e}var M=function(u,y){var f=B({},u,y.attrs);return d(re,B({},f,{icon:ve}),null)};M.displayName="UploadOutlined";M.inheritAttrs=!1;const D=M,N=e=>[{label:e("runForm.customName"),name:"customer",default:void 0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"string"},{label:e("runForm.proName"),name:"projectName",default:void 0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"string"},{label:e("runForm.proBg"),name:"projectBackground",default:void 0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"textarea"},{label:e("runForm.customRequire"),name:"requirementDesc",default:void 0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"textarea"}],x=(e,u)=>[{label:e("runForm.rateOfCharge"),name:"rateOfCharge",default:.94,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:""},{label:e("runForm.rateOfDischarge"),name:"rateOfDischarge",default:.92,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:""},{label:e("runForm.batDepth",{currency:u}),name:"batDepth",default:.94,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"元/Kwh"},{label:e("runForm.batMagnification"),name:"batMagnification",default:.5,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:""},{label:e("runForm.soc"),name:"soc",default:.2,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:""},{label:e("runForm.minConfigRateOfBat"),name:"minConfigRateOfBat",default:.2137,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:""},{label:e("runForm.batConfidence"),name:"batConfidence",default:.996,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"h"},{label:e("runForm.minLoadOfAlk"),name:"minLoadOfAlk",default:.3,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:""},{label:e("runForm.wasteH2"),name:"wasteH2",default:200,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:""},{label:e("runForm.dampRateOfAlk"),name:"dampRateOfAlk",default:.005,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:""},{label:e("runForm.rateOfH2Power"),name:"rateOfH2Power",default:.97,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:""},{label:e("runForm.ecOfAssitSys"),name:"ecOfAssitSys",default:.3,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"Kwh/nm³"},{label:e("runForm.dampRateOfPv"),name:"dampRateOfPv",default:.0055,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:""},{label:e("runForm.maxRate"),name:"maxRate",default:.2,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:""}],H=(e,u)=>[{label:e("runForm.h2Production"),name:"hpy",default:void 0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"万吨"},{label:e("runForm.loadLack"),name:"lossRateOfLoad",default:0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"",tooltip:"8760小时未被利用的小时数"},{label:e("runForm.alkConsum"),name:"fixedEcOfAlk",default:void 0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"Kwh/nm³"},{label:e("runForm.h2Price",{currency:u}),name:"h2Price",default:4.1,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"/nm³"}],G=e=>[{label:e("runForm.pvCapacityUp"),name:"pvUL",default:void 0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"MWp"},{label:e("runForm.pvCapacityLow"),name:"pvLL",default:void 0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"MWp"},{label:e("runForm.StorageCapacityUp"),name:"batUL",default:void 0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"Mwh"},{label:e("runForm.StorageCapacityLow"),name:"batLL",default:void 0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"Mwh"},{label:e("runForm.windCapacityUp"),name:"wpUL",default:void 0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"MW"},{label:e("runForm.windCapacityLow"),name:"wpLL",default:void 0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"MW"},{label:e("runForm.ALKCapacityUp"),name:"alkUL",default:void 0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"MW"},{label:e("runForm.ALKCapacityLow"),name:"alkLL",default:void 0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"MW"}],J=(e,u)=>[{label:e("runForm.pvEPC",{currency:u}),name:"pvEpcInvest",default:2.9,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"元/W"},{label:e("runForm.pvOperation",{currency:u}),name:"pvOpertCost",default:.25,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"元/W/年"},{label:e("runForm.storageEPC",{currency:u}),name:"batEpcInvest",default:1.3,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"元/W"},{label:e("runForm.storageOperation",{currency:u}),name:"batOpertCost",default:.0675,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"元/W/年"},{label:e("runForm.windEPC",{currency:u}),name:"wpEpcInvest",default:4.2,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"元/W"},{label:e("runForm.windOperation",{currency:u}),name:"wpOpertCost",default:.3,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"元/W/年"},{label:e("runForm.ALKInvest",{currency:u}),name:"h2SysInvest",default:2,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"元/W"},{label:e("runForm.ALKOperaiton"),name:"h2SysOpertRadio",default:.02,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:""},{label:e("runForm.ALKFactory",{currency:u}),name:"h2PlantInvest",default:1.5,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"元/W"}],Q=(e,u)=>[{label:e("runForm.projectCycle"),name:"projCycle",default:25,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"年"},{label:e("runForm.loanRatio"),name:"loanRadio",default:.7,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:""},{label:e("runForm.loanCycle"),name:"loanCycle",default:15,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"年"},{label:e("runForm.lendingRate"),name:"loanRate",default:.049,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:""},{label:e("runForm.waterPrice",{currency:u}),name:"waterPrice",default:4.38,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"元/吨"},{label:e("runForm.waterConsumption"),name:"waterCost",default:1.4,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"L/nm³"},{label:e("runForm.elePrice",{currency:u}),name:"avgPriceOfGrid",default:.45,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"元/Kwh"},{label:e("runForm.maxTime"),name:"maxHourOfGrid",default:0,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"h"},{label:e("runForm.greenPrice",{currency:u}),name:"priceOfGreenElectric",default:.332,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:"元/Kwh"},{label:e("runForm.discountRate"),name:"discountRate",default:.06,rules:[{required:!0,message:e("base.pleaseInput")}],type:"number",unit:""}],ye=e=>[{title:`${e("runForm.specification")}(Nm³)`,dataIndex:"Specification"},{title:`${e("runForm.powerConsumption")}(kwh/Nm³)`,dataIndex:"powerConsumption"},{title:`${e("runForm.ratedPower")}(MW)`,dataIndex:"ratedPower"}],ge={class:"wrapper"},we={class:"tool_wrap"},Fe={class:"form_input_wrap"},Ie={class:"form1 form_column"},he={class:"card_item card_item11"},Oe={class:"card_title"},qe={class:"card_item card_item12"},ke={class:"card_title"},Ce={class:"form3 form_column"},Ue={class:"card_item card_item31"},Le={class:"card_title"},Pe={href:"void:0"},Re={class:"card_item card_item32"},Ve={class:"card_title"},ze={class:"form4 form_column"},Ee={class:"card_item card_item41"},Se={class:"card_title"},Ke={class:"card_item card_item42"},je={class:"card_title"},Ae={class:"form2 form_column"},Me={class:"card_item card_item21"},We={class:"card_title"},Te={class:"button_wrap"},Be=te({__name:"index",setup(e){const u=T(),{locale:y}=T(),f=ne(),F=k(),S=k(),R=k(!1),X=se(),r=k({}),K=k([]);k([]);const Y=async()=>{var P,V,z;console.log("currency:",f.rate,f.currency);const l=await F.value.validateFields(),c=new FormData;if(!((P=l.pvPower)!=null&&P.length)&&!((V=l.wpPower)!=null&&V.length)){E.warn(u.t("runForm.tipsEmpty"));return}if(((z=q.selectedRowKeys)==null?void 0:z.length)<1){E.warn(u.t("runForm.alkEmpty"));return}Object.entries(l).forEach(([v,I])=>{["pvPower","wpPower"].includes(v)?c.append(v,I==null?void 0:I[0]):["pvEpcInvest","pvOpertCost","batEpcInvest","batOpertCost","wpEpcInvest","wpOpertCost","h2SysInvest","h2PlantInvest","waterPrice","avgPriceOfGrid","batDepth","h2Price"].includes(v)?c.append(v,I*f.rate):c.append(v,I)}),c.append("iterations",100),c.append("electrolyzers",q.selectedRowKeys),console.log("Success:",l);for(var[j,g]of c.entries())console.log(j,g);S.value=!0;const{code:w,msg:A,data:b}=await ce(c);if(S.value=!1,w===0){E.success(u.t("runForm.tipsToRun"));const v=X.resolve({name:"runResult",params:{taskId:b==null?void 0:b.taskId},query:{type:"0"}});window.open(v.href,"_blank")}else E.error(A)},Z=()=>{r.value={...Object.fromEntries(H(u.t).map(l=>[l.name,l.default])),...Object.fromEntries(J(u.t,f.currency).map(l=>[l.name,l.default])),...Object.fromEntries(Q(u.t).map(l=>[l.name,l.default])),...Object.fromEntries(N(u.t).map(l=>[l.name,l.default])),...Object.fromEntries(G(u.t).map(l=>[l.name,l.default])),...Object.fromEntries(x(u.t).map(l=>[l.name,l.default])),taskType:1,customer:"test1",projectName:"p1",projectBackground:"background",requirementDesc:"description",pvUL:120,pvLL:120,batUL:0,batLL:0,wpUL:0,wpLL:0,alkUL:60,alkLL:60,hpy:.27,fixedEcOfAlk:4.3}},$=()=>{R.value=!0},ee=l=>{console.log(l),R.value=!1},q=oe({selectedRowKeys:[],loading:!1});pe(()=>q.selectedRowKeys.length>0);const ae=l=>{console.log("selectedRowKeys changed: ",l),q.selectedRowKeys=l},le=async()=>{const{data:{result:l}}=await fe();K.value=l,console.log("electrolyzers:",K)};return de(()=>{Z(),le()}),(l,c)=>{const j=_("a-popover"),g=_("a-input-number"),w=_("a-input"),A=_("a-textarea"),b=_("a-form-item"),P=_("a-radio-button"),V=_("a-radio-group"),z=_("a-table"),v=_("a-button"),I=_("a-modal"),W=_("a-upload"),ue=_("a-form");return s(),h("div",null,[t("div",ge,[t("div",we,[d(j,{title:"Tips"},{content:n(()=>[t("div",null,i(l.$t("runForm.tipsOption1")),1),t("div",null,i(l.$t("runForm.tipsOption2")),1),t("div",null,i(l.$t("runForm.tipsOption3")),1)]),default:n(()=>[d(p(me),{class:"icon_tips"})]),_:1})]),d(ue,{ref_key:"formRef",ref:F,model:r.value,name:"basic","label-col":{span:p(y)==="zh_CN"?15:18},"wrapper-col":{span:9},autocomplete:"off"},{default:n(()=>[t("div",Fe,[t("div",Ie,[t("div",he,[t("div",Oe,i(l.$t("runForm.title1")),1),(s(!0),h(C,null,U(p(N)(p(u).t),a=>(s(),m(b,{class:"item_wrap",label:a.label,name:a.name,rules:a.rules,key:a.name},{default:n(()=>[a.type==="number"?(s(),m(g,{key:0,class:"input_deal_wrap",defaultValue:a.default,value:r.value[a.name],"onUpdate:value":o=>r.value[a.name]=o,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):a.type==="string"?(s(),m(w,{key:1,class:"input_deal_wrap",defaultValue:a.default,value:r.value[a.name],"onUpdate:value":o=>r.value[a.name]=o,size:"small"},null,8,["defaultValue","value","onUpdate:value"])):a.type==="textarea"?(s(),m(A,{key:2,class:"input_deal_wrap",style:{margin:"5px 0 5px 0"},autosize:{maxRows:2},defaultValue:a.default,value:r.value[a.name],"onUpdate:value":o=>r.value[a.name]=o,size:"small"},null,8,["defaultValue","value","onUpdate:value"])):L("",!0)]),_:2},1032,["label","name","rules"]))),128)),d(b,{class:"item_wrap",label:l.$t("runForm.taskType"),name:"taskType",rules:[{required:!0}],key:"taskType",style:{position:"relative"}},{default:n(()=>[d(V,{size:"small",value:r.value.taskType,"onUpdate:value":c[0]||(c[0]=a=>r.value.taskType=a),"button-style":"solid",class:"select_radio radio_wrap"},{default:n(()=>[d(P,{value:1},{default:n(()=>[O(i(l.$t("base.gridOff")),1)]),_:1}),d(P,{value:2},{default:n(()=>[O(i(l.$t("base.gridConnected")),1)]),_:1})]),_:1},8,["value"])]),_:1},8,["label"])]),t("div",qe,[t("div",ke,i(l.$t("runForm.title2")),1),(s(!0),h(C,null,U(p(G)(p(u).t),a=>(s(),m(b,{class:"item_wrap",label:a.label,name:a.name,rules:a.rules,key:a.name},{default:n(()=>[a.type==="number"?(s(),m(g,{key:0,class:"input_deal_wrap",defaultValue:a.default,value:r.value[a.name],"onUpdate:value":o=>r.value[a.name]=o,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):a.type==="string"?(s(),m(w,{key:1,defaultValue:a.default,value:r.value[a.name],"onUpdate:value":o=>r.value[a.name]=o,size:"small"},null,8,["defaultValue","value","onUpdate:value"])):L("",!0)]),_:2},1032,["label","name","rules"]))),128))])]),t("div",Ce,[t("div",Ue,[t("div",Le,i(l.$t("runForm.title3")),1),(s(!0),h(C,null,U(p(H)(p(u).t,p(f).currency),a=>(s(),m(b,{class:"item_wrap",label:a.label,name:a.name,rules:a.rules,key:a.name,tooltip:a.tooltip},{default:n(()=>[a.type==="number"?(s(),m(g,{key:0,class:"input_deal_wrap",defaultValue:a.default,value:r.value[a.name],"onUpdate:value":o=>r.value[a.name]=o,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):a.type==="string"?(s(),m(w,{key:1,defaultValue:a.default,value:r.value[a.name],"onUpdate:value":o=>r.value[a.name]=o,size:"small"},null,8,["defaultValue","value","onUpdate:value"])):L("",!0)]),_:2},1032,["label","name","rules","tooltip"]))),128)),d(b,{class:"item_wrap",label:l.$t("runForm.alkType"),rules:[{required:!0}]},{default:n(()=>[t("div",{onClick:$,style:{fontSize:"12px"}},[t("a",Pe,i(l.$t("runForm.selectALKTip")),1),O(": "+i(`${q.selectedRowKeys.length}`),1)]),d(I,{open:R.value,"onUpdate:open":c[1]||(c[1]=a=>R.value=a),title:l.$t("runForm.alkType")},{footer:n(()=>[d(v,{type:"primary",onClick:ee},{default:n(()=>[O("Sure")]),_:1})]),default:n(()=>[d(z,{size:"small","row-selection":{selectedRowKeys:q.selectedRowKeys,onChange:ae},columns:p(ye)(p(u).t),"data-source":K.value,rowKey:"id",pagination:!1},null,8,["row-selection","columns","data-source"])]),_:1},8,["open","title"])]),_:1},8,["label"])]),t("div",Re,[t("div",Ve,i(l.$t("runForm.title4")),1),(s(!0),h(C,null,U(p(J)(p(u).t,p(f).currency),a=>(s(),m(b,{class:"item_wrap",label:a.label,name:a.name,rules:a.rules,key:a.name},{default:n(()=>[a.type==="number"?(s(),m(g,{key:0,class:"input_deal_wrap",defaultValue:a.default,value:r.value[a.name],"onUpdate:value":o=>r.value[a.name]=o,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):a.type==="string"?(s(),m(w,{key:1,defaultValue:a.default,value:r.value[a.name],"onUpdate:value":o=>r.value[a.name]=o,size:"small"},null,8,["defaultValue","value","onUpdate:value"])):L("",!0)]),_:2},1032,["label","name","rules"]))),128))])]),t("div",ze,[t("div",Ee,[t("div",Se,i(l.$t("runForm.title5")),1),(s(!0),h(C,null,U(p(Q)(p(u).t,p(f).currency),a=>(s(),m(b,{class:"item_wrap",label:a.label,name:a.name,rules:a.rules,key:a.name},{default:n(()=>[a.type==="number"?(s(),m(g,{key:0,class:"input_deal_wrap",defaultValue:a.default,value:r.value[a.name],"onUpdate:value":o=>r.value[a.name]=o,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):a.type==="string"?(s(),m(w,{key:1,defaultValue:a.default,value:r.value[a.name],"onUpdate:value":o=>r.value[a.name]=o,size:"small"},null,8,["defaultValue","value","onUpdate:value"])):L("",!0)]),_:2},1032,["label","name","rules"]))),128))]),t("div",Ke,[t("div",je,i(l.$t("runForm.title6")),1),d(b,{name:"pvPower",label:l.$t("runForm.pvPower"),rules:[{required:!1,message:l.$t("runForm.upload")}],tooltip:"0-1之间的8760h的光伏标幺值"},{default:n(()=>[d(W,{"before-upload":a=>(r.value.pvPower=[a],!1),onRemove:c[2]||(c[2]=()=>{r.value.pvPower=[]}),accept:".txt",maxCount:1},{default:n(()=>[d(v,{size:"small",style:{fontSize:"11px"}},{icon:n(()=>[d(p(D))]),default:n(()=>[O(" "+i(l.$t("runForm.upload")),1)]),_:1})]),_:1},8,["before-upload"])]),_:1},8,["label","rules"]),d(b,{name:"wpPower",label:l.$t("runForm.windPower"),rules:[{required:!1,message:l.$t("runForm.upload")}],tooltip:"0-1之间的8760h的风电标幺值"},{default:n(()=>[d(W,{"before-upload":a=>(r.value.wpPower=[a],!1),onRemove:c[3]||(c[3]=()=>{r.value.wpPower=[]}),accept:".txt",maxCount:1},{default:n(()=>[d(v,{size:"small",style:{fontSize:"11px"}},{icon:n(()=>[d(p(D))]),default:n(()=>[O(" "+i(l.$t("runForm.upload")),1)]),_:1})]),_:1},8,["before-upload"])]),_:1},8,["label","rules"])])]),t("div",Ae,[t("div",Me,[t("div",We,i(l.$t("runForm.title7")),1),(s(!0),h(C,null,U(p(x)(p(u).t,p(f).currency),a=>(s(),m(b,{class:"item_wrap",label:a.label,name:a.name,rules:a.rules,key:a.name},{default:n(()=>[a.type==="number"?(s(),m(g,{key:0,class:"input_deal_wrap",defaultValue:a.default,value:r.value[a.name],"onUpdate:value":o=>r.value[a.name]=o,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):a.type==="string"?(s(),m(w,{key:1,defaultValue:a.default,value:r.value[a.name],"onUpdate:value":o=>r.value[a.name]=o,size:"small"},null,8,["defaultValue","value","onUpdate:value"])):L("",!0)]),_:2},1032,["label","name","rules"]))),128))])])])]),_:1},8,["model","label-col"]),t("div",Te,[d(v,{onClick:Y,type:"primary",block:"",loading:S.value},{default:n(()=>[O(i(l.$t("runForm.run")),1)]),_:1},8,["loading"])])])])}}}),xe=ie(Be,[["__scopeId","data-v-3885567a"],["__file","E:/02 ems code/LCOH/frontend/src/views/RunForm/index.vue"]]);export{xe as default};
