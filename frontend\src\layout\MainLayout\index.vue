<template>
  <a-layout>
    <a-layout-header style="padding: 0;height: auto;line-height: normal;">
      <Header></Header>
    </a-layout-header>
    <a-layout>
      <a-layout-sider
        @collapse="toggleCollapsed"
        width="150"
        collapsible
        theme="light"
        :style="{ overflow: 'auto', height: 'calc(100vh - 64px)', background: '#fff' }"
        :collapsed="baseStore.menuCollapsed"
      >
        <!-- Debug info (remove in production) -->
        <div style="padding: 10px; font-size: 12px; background: #f0f0f0; margin-bottom: 10px;" v-if="false">
          <div>Route: {{ route.name }}</div>
          <div>Meta Key: {{ route.meta?.key }}</div>
          <div>Selected Keys: {{ state.selectedKeys }}</div>
        </div>
        
        <a-menu
          mode="inline"
          @click="navTo"
          v-model:openKeys="state.openKeys"
          v-model:selectedKeys="state.selectedKeys"
          :items="MenuItems()">
        </a-menu>
        <!-- <Menu :inlineCollapsed="collapsed" /> -->
      </a-layout-sider>
      <a-layout-content style="margin: 0;height: calc(100vh - 64px);overflow: auto;">
        <RouterView />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { RouterView } from 'vue-router';
import MenuItems from '@/components/Menu/menuConfig'
import Header from '@/components/Header/index.vue'
import { useBaseStore } from '@/stores/base'
import { useRouter, useRoute } from 'vue-router';

const baseStore = useBaseStore()
const router = useRouter();
const route = useRoute()

const collapsed = ref<boolean>(false);
interface StateType {
  collapsed: boolean;
  selectedKeys: string[];
  openKeys: string[];
  preOpenKeys: string[];
}

const state = reactive<StateType>({
  collapsed: false,
  selectedKeys: [],
  openKeys: [],
  preOpenKeys: [],
});
const toggleCollapsed = () => {
  // state.collapsed = !state.collapsed;
  console.log(222)
  baseStore.collapseMenu()
  state.openKeys = baseStore.menuCollapsed ? [] : state.preOpenKeys;
};


const navTo = (route: { key: string }) => {
  console.log('r:', route)
  router.push({
    name: route.key,
  });
};
// 设置菜单选中状态的通用函数
const setSelectedMenu = (routeKey: string) => {
  console.log('Setting selected menu:', routeKey)
  if (routeKey) {
    state.selectedKeys = [routeKey]
    console.log('Selected keys set to:', state.selectedKeys)
  }
}

// 临时调试函数
const debugRoute = (currentRoute: any) => {
  console.log('=== 路由调试信息 ===')
  console.log('Route name:', currentRoute.name)
  console.log('Route path:', currentRoute.path)
  console.log('Route meta:', currentRoute.meta)
  console.log('Route meta.key:', currentRoute.meta?.key)
  console.log('Route meta type:', typeof currentRoute.meta?.key)
  console.log('Is meta.key === "economicAnalysis":', currentRoute.meta?.key === 'economicAnalysis')
  console.log('===================')
}

watch(route, (newRoute) => {
  console.log('Route changed:', {
    name: newRoute.name,
    path: newRoute.path,
    metaKey: newRoute.meta?.key
  })
  debugRoute(newRoute)
  setSelectedMenu(newRoute.meta?.key as string)
}, { deep: true, immediate: true })

onMounted(() => {
  console.log('MainLayout mounted, current route:', {
    name: route.name,
    path: route.path,
    metaKey: route.meta?.key
  })
  debugRoute(route)
  setSelectedMenu(route.meta?.key as string)
  
  if (baseStore.menuCollapsed) {
    state.openKeys = []
  }
})
</script>
<style scoped lang="less">
@import '@/style/base.less';

#components-layout-demo-side .logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
}

.site-layout .site-layout-background {
  background: #fff;
}
/* [data-theme='dark'] .site-layout .site-layout-background {
  background: #141414;
} */
:deep(.ant-menu-item-selected) {
  color: #fff;
  background-color: @baseColor;
}
</style>
