import {
  PieChartOutlined,
  DesktopOutlined,
  AppstoreOutlined,
  AlertOutlined,
  StockOutlined,
  UserOutlined,
  Bar<PERSON>hartOutlined,
  NodeIndexOutlined,
  VideoCameraAddOutlined,
  FileProtectOutlined,
  RiseOutlined,
  ControlOutlined,
  FundOutlined
} from '@ant-design/icons-vue'
import { h } from 'vue'
// import i18n from '@/i18n'

const items = () => [
    {
      key: 'home',
      icon: () => h(PieChartOutlined),
      label:  '总览',
      title: 'Home',
      // disabled: true
    },
    {
      key: 'projectList',
      icon: () => h(DesktopOutlined),
      label:  '容量测算',
      title: '容量测算',
      // disabled: true
    },
     {
      key: 'economicAnalysis',
      icon: () => h(FundOutlined),
      label:  '经济分析',
      title: '经济分析',
      // disabled: true
    },
    {
      key: 'deviceConfig',
      icon: () => h(ControlOutlined),
      label:  '设备配置',
      title: '设备配置',
      // disabled: true
    },
    // {
    //   key: '容量测算',
    //   icon: () => h(VideoCameraAddOutlined),
    //   label: '基础配置',
    //   title: '控制策略',
    //   // disabled: true,
    //   children: [
    //     {
    //       key: 'manage',
    //       label: '策略管理',
    //       title: '策略管理',
    //     },
    //     {
    //       key: 'monitor',
    //       label: '策略监控',
    //       title: '策略监控',
    //     },
    //   ],
    // },
    // {
    //   key: 'login',
    //   icon: () => h(PieChartOutlined),
    //   label:  '登录',
    //   title: 'login',
    //   // disabled: true
    // },
];

export default items;