import{Q as b,r as S,e as x,d as y,z as E,f as o,b as c,c as p,g as d,t as V,i as k,k as C,h as e,w as t,n as I,R,v as B,E as D,G as N,_ as L}from"./index-C98MXVsn.js";const P="/assets/login_logo-CxGtSMKW.png";var U={BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1};const q=n=>(D("data-v-51062842"),n=n(),N(),n),z={class:"bg_wrap"},A={class:"login_window"},G={key:0,class:"title"},M=q(()=>d("img",{src:P,class:"img_wrap"},null,-1)),O=[M],T={key:1,class:"title"},F=b({__name:"index",setup(n){const l=S(!1),m=x(()=>U.VITE_APP_ENV==="zh"),f=y(),a=E({username:"",password:""}),g=async u=>{l.value=!0;const{code:s,msg:r}=await R(u);l.value=!1,s===0?(console.log("login success"),f.push({name:"home"})):B.error(r)};return(u,s)=>{const r=o("a-input"),_=o("a-form-item"),v=o("a-input-password"),w=o("a-button"),h=o("a-form");return c(),p("div",z,[d("div",A,[m.value?(c(),p("div",G,O)):(c(),p("div",T,V(k(C).title),1)),e(h,{class:"form_wrap",model:a,name:"basic","label-col":{span:6},"wrapper-col":{span:14},autocomplete:"off",onFinish:g},{default:t(()=>[e(_,{label:"用户名",name:"username",rules:[{required:!0,message:"请输入用户名!"}]},{default:t(()=>[e(r,{value:a.username,"onUpdate:value":s[0]||(s[0]=i=>a.username=i)},null,8,["value"])]),_:1}),e(_,{label:"密码",name:"password",rules:[{required:!0,message:"请输入密码!"}]},{default:t(()=>[e(v,{value:a.password,"onUpdate:value":s[1]||(s[1]=i=>a.password=i)},null,8,["value"])]),_:1}),e(_,{"wrapper-col":{offset:6,span:14}},{default:t(()=>[e(w,{loading:l.value,style:{width:"100%"},type:"primary","html-type":"submit"},{default:t(()=>[I(" 登录 ")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])])])}}}),Q=L(F,[["__scopeId","data-v-51062842"]]);export{Q as default};
