<template>
  <div class="map-selector">
    <!-- 地址输入区域 -->
    <div class="address-input-section">
      <div class="input-group">
        <a-input
          v-model:value="addressInput"
          placeholder="请输入项目地址，如：北京市朝阳区"
          size="small"
          style="width: 300px; margin-right: 8px;"
          @pressEnter="searchAddress"
        />
        <a-button type="primary" size="small" @click="searchAddress" :loading="searching">
          搜索
        </a-button>
        <a-button size="small" @click="getCurrentLocation" :loading="locating" style="margin-left: 8px;">
          定位
        </a-button>
        <a-button size="small" @click="clearAddress" style="margin-left: 8px;">
          清空
        </a-button>
      </div>
      <div v-if="selectedAddress" class="selected-address">
        <span class="address-label">项目地址：</span>
        <span class="address-text">{{ selectedAddress }}</span>
        <span v-if="coordinates.lng && coordinates.lat" class="coordinates">
          （{{ coordinates.lng.toFixed(6) }}, {{ coordinates.lat.toFixed(6) }}）
        </span>
      </div>
      <div class="address-tips">
        <span class="tips-text">提示：输入地址后点击搜索，可在地图上查看位置并进行精确定位</span>
      </div>
    </div>

    <!-- 搜索结果列表 -->
    <div v-if="searchResults.length > 0" class="search-results">
      <div class="results-title">搜索结果（点击选择）：</div>
      <div class="results-list">
        <div
          v-for="(result, index) in searchResults"
          :key="index"
          class="result-item"
          @click="selectSearchResult(result)"
        >
          <div class="result-name">{{ result.title }}</div>
          <div class="result-address">{{ result.address }}</div>
        </div>
      </div>
    </div>

    <!-- 腾讯地图容器 -->
    <div class="map-container">
      <div id="tencent-map" ref="mapContainer" style="width: 100%; height: 400px;"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { message } from 'ant-design-vue';

// 腾讯地图API Key
const TENCENT_MAP_KEY = 'YFJBZ-LMBCU-E7GVE-4A3I7-W6H55-W4FFR';

// Props
interface Props {
  modelValue?: {
    address: string;
    lng: number;
    lat: number;
  };
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({ address: '', lng: 0, lat: 0 })
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: { address: string; lng: number; lat: number }];
  'change': [value: { address: string; lng: number; lat: number }];
}>();

// 响应式数据
const addressInput = ref('');
const selectedAddress = ref('');
const coordinates = ref({ lng: 0, lat: 0 });
const searching = ref(false);
const locating = ref(false);
const searchResults = ref<any[]>([]);
const mapContainer = ref<HTMLElement>();

// 腾讯地图相关变量
let map: any = null;
let marker: any = null;
let geocoder: any = null;
let TMap: any = null;

// 动态加载腾讯地图API
const loadTencentMapAPI = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    if (window.TMap) {
      resolve(window.TMap);
      return;
    }

    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = `https://map.qq.com/api/gljs?v=1.exp&key=${TENCENT_MAP_KEY}&libraries=service&callback=onTMapCallback`;
    script.onerror = reject;

    // 设置全局回调
    (window as any).onTMapCallback = () => {
      resolve(window.TMap);
    };

    document.head.appendChild(script);
  });
};

// 初始化地图
const initMap = async () => {
  try {
    TMap = await loadTencentMapAPI();

    if (!mapContainer.value) {
      console.error('地图容器未找到');
      return;
    }

    console.log('TMap对象:', TMap);
    console.log('TMap.service:', TMap.service);

    // 创建地图实例
    map = new TMap.Map('tencent-map', {
      center: new TMap.LatLng(39.908823, 116.397470), // 默认北京
      zoom: 10,
      mapStyleId: 'style1' // 标准地图样式
    });

    // 检查service是否可用
    if (TMap.service && TMap.service.Geocoder) {
      // 创建地理编码器
      geocoder = new TMap.service.Geocoder({
        complete: (result: any) => {
          console.log('地理编码完成:', result);
        },
        error: (error: any) => {
          console.error('地理编码错误:', error);
        }
      });
      console.log('地理编码器创建成功');
    } else {
      console.warn('TMap.service.Geocoder 不可用，将使用备用搜索方案');
    }

    // 地图点击事件
    map.on('click', (evt: any) => {
      const { lat, lng } = evt.latLng;
      updateLocation(lng, lat);
      reverseGeocode(lng, lat);
    });

    console.log('腾讯地图初始化成功');
  } catch (error) {
    console.error('地图初始化失败:', error);
    message.error('地图加载失败，请检查网络连接');
  }
};

// 搜索地址
const searchAddress = async () => {
  if (!addressInput.value.trim()) {
    message.warning('请输入地址');
    return;
  }

  searching.value = true;
  searchResults.value = [];

  try {
    // 先尝试使用地理编码
    if (geocoder) {
      geocoder.getLocation({
        address: addressInput.value.trim(),
        success: (result: any) => {
          searching.value = false;
          if (result.result && result.result.location) {
            const location = result.result.location;
            const address = addressInput.value.trim();

            // 创建搜索结果
            searchResults.value = [{
              title: address,
              address: address,
              location: {
                lng: location.lng,
                lat: location.lat
              }
            }];

            message.success('地址解析成功');
          } else {
            // 如果地理编码失败，使用模糊搜索
            performFuzzySearch();
          }
        },
        error: (error: any) => {
          console.error('地理编码失败:', error);
          // 地理编码失败时，使用模糊搜索
          performFuzzySearch();
        }
      });
    } else {
      // 如果地理编码器不可用，直接使用模糊搜索
      performFuzzySearch();
    }

  } catch (error) {
    searching.value = false;
    console.error('搜索异常:', error);
    performFuzzySearch();
  }
};

// 模糊搜索功能
const performFuzzySearch = () => {
  const keyword = addressInput.value.trim().toLowerCase();

  // 预定义常见城市和地区的坐标
  const locationData: { [key: string]: { lng: number; lat: number; fullName: string } } = {
    // 直辖市
    '北京': { lng: 116.397428, lat: 39.90923, fullName: '北京市' },
    '上海': { lng: 121.473701, lat: 31.230416, fullName: '上海市' },
    '天津': { lng: 117.190182, lat: 39.125596, fullName: '天津市' },
    '重庆': { lng: 106.504962, lat: 29.533155, fullName: '重庆市' },

    // 省会城市
    '广州': { lng: 113.280637, lat: 23.125178, fullName: '广东省广州市' },
    '深圳': { lng: 114.085947, lat: 22.547, fullName: '广东省深圳市' },
    '杭州': { lng: 120.153576, lat: 30.287459, fullName: '浙江省杭州市' },
    '南京': { lng: 118.767413, lat: 32.041544, fullName: '江苏省南京市' },
    '武汉': { lng: 114.298572, lat: 30.584355, fullName: '湖北省武汉市' },
    '成都': { lng: 104.065735, lat: 30.659462, fullName: '四川省成都市' },
    '西安': { lng: 108.948024, lat: 34.263161, fullName: '陕西省西安市' },
    '郑州': { lng: 113.665412, lat: 34.757975, fullName: '河南省郑州市' },
    '济南': { lng: 117.000923, lat: 36.675807, fullName: '山东省济南市' },
    '青岛': { lng: 120.355173, lat: 36.082982, fullName: '山东省青岛市' },
    '大连': { lng: 121.618622, lat: 38.91459, fullName: '辽宁省大连市' },
    '沈阳': { lng: 123.429096, lat: 41.796767, fullName: '辽宁省沈阳市' },
    '长春': { lng: 125.3245, lat: 43.886841, fullName: '吉林省长春市' },
    '哈尔滨': { lng: 126.642464, lat: 45.756967, fullName: '黑龙江省哈尔滨市' },
    '昆明': { lng: 102.712251, lat: 25.040609, fullName: '云南省昆明市' },
    '贵阳': { lng: 106.713478, lat: 26.578343, fullName: '贵州省贵阳市' },
    '兰州': { lng: 103.823557, lat: 36.058039, fullName: '甘肃省兰州市' },
    '银川': { lng: 106.278179, lat: 38.46637, fullName: '宁夏银川市' },
    '西宁': { lng: 101.778916, lat: 36.623178, fullName: '青海省西宁市' },
    '乌鲁木齐': { lng: 87.617733, lat: 43.792818, fullName: '新疆乌鲁木齐市' },
    '拉萨': { lng: 91.132212, lat: 29.660361, fullName: '西藏拉萨市' },
    '呼和浩特': { lng: 111.670801, lat: 40.818311, fullName: '内蒙古呼和浩特市' },
    '南宁': { lng: 108.320004, lat: 22.82402, fullName: '广西南宁市' },
    '海口': { lng: 110.35119, lat: 20.031971, fullName: '海南省海口市' },
    '三亚': { lng: 109.508268, lat: 18.247872, fullName: '海南省三亚市' },
    '福州': { lng: 119.306239, lat: 26.075302, fullName: '福建省福州市' },
    '厦门': { lng: 118.11022, lat: 24.490474, fullName: '福建省厦门市' },
    '南昌': { lng: 115.892151, lat: 28.676493, fullName: '江西省南昌市' },
    '长沙': { lng: 112.982279, lat: 28.19409, fullName: '湖南省长沙市' },
    '合肥': { lng: 117.283042, lat: 31.86119, fullName: '安徽省合肥市' },
    '石家庄': { lng: 114.502461, lat: 38.045474, fullName: '河北省石家庄市' },
    '太原': { lng: 112.549248, lat: 37.857014, fullName: '山西省太原市' }
  };

  // 查找匹配的城市（支持模糊匹配）
  const matchedLocations = Object.keys(locationData).filter(city => {
    const cityLower = city.toLowerCase();
    const fullNameLower = locationData[city].fullName.toLowerCase();
    return cityLower.includes(keyword) ||
           keyword.includes(cityLower) ||
           fullNameLower.includes(keyword) ||
           keyword.includes(fullNameLower.replace(/省|市|自治区/g, ''));
  });

  if (matchedLocations.length > 0) {
    searchResults.value = matchedLocations.slice(0, 5).map(city => ({
      title: city,
      address: locationData[city].fullName,
      location: {
        lng: locationData[city].lng,
        lat: locationData[city].lat
      }
    }));
    searching.value = false;
    message.success(`找到 ${matchedLocations.length} 个匹配地点`);
  } else {
    searching.value = false;
    message.warning('未找到相关地址，请尝试输入具体的城市名称（如：北京、上海、广州等）');
  }
};

// 选择搜索结果
const selectSearchResult = (result: any) => {
  const { location, title, address } = result;
  const lng = location.lng;
  const lat = location.lat;

  selectedAddress.value = `${title} ${address}`;
  addressInput.value = selectedAddress.value;
  searchResults.value = [];

  updateLocation(lng, lat);

  // 地图移动到选中位置
  if (map) {
    map.setCenter(new TMap.LatLng(lat, lng));
    map.setZoom(15);
  }

  message.success('地址已选择');
};

// 更新位置
const updateLocation = (lng: number, lat: number) => {
  coordinates.value = {
    lng: Number(lng.toFixed(6)),
    lat: Number(lat.toFixed(6))
  };

  // 更新或创建标记
  if (marker) {
    marker.setPosition(new TMap.LatLng(lat, lng));
  } else if (map && TMap) {
    marker = new TMap.MultiMarker({
      map: map,
      styles: {
        'marker': new TMap.MarkerStyle({
          'width': 25,
          'height': 35,
          'anchor': { x: 16, y: 32 },
          'src': 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/markerDefault.png'
        })
      },
      geometries: [{
        id: 'project-location',
        styleId: 'marker',
        position: new TMap.LatLng(lat, lng),
        properties: {
          title: '项目位置'
        }
      }]
    });
  }

  // 发送更新事件
  const value = {
    address: selectedAddress.value,
    lng: coordinates.value.lng,
    lat: coordinates.value.lat
  };

  emit('update:modelValue', value);
  emit('change', value);
};

// 逆地理编码
const reverseGeocode = (lng: number, lat: number) => {
  if (!geocoder) return;

  geocoder.getAddress({
    location: new TMap.LatLng(lat, lng),
    success: (result: any) => {
      if (result.result && result.result.formatted_addresses) {
        const address = result.result.formatted_addresses.recommend ||
                       result.result.formatted_addresses.rough;
        if (address) {
          selectedAddress.value = address;
          addressInput.value = address;
        }
      }
    },
    error: (error: any) => {
      console.error('逆地理编码失败:', error);
    }
  });
};

// 获取当前位置
const getCurrentLocation = () => {
  if (!navigator.geolocation) {
    message.error('浏览器不支持定位功能');
    return;
  }

  locating.value = true;
  navigator.geolocation.getCurrentPosition(
    (position) => {
      locating.value = false;
      const { longitude, latitude } = position.coords;

      updateLocation(longitude, latitude);
      reverseGeocode(longitude, latitude);

      if (map) {
        map.setCenter(new TMap.LatLng(latitude, longitude));
        map.setZoom(15);
      }

      message.success('定位成功');
    },
    (error) => {
      locating.value = false;
      console.error('定位失败:', error);
      message.error('定位失败，请手动输入地址或检查定位权限');
    },
    {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 60000
    }
  );
};

// 清空地址
const clearAddress = () => {
  addressInput.value = '';
  selectedAddress.value = '';
  coordinates.value = { lng: 0, lat: 0 };
  searchResults.value = [];

  // 清除地图标记
  if (marker) {
    marker.setMap(null);
    marker = null;
  }

  // 重置地图中心
  if (map) {
    map.setCenter(new TMap.LatLng(39.908823, 116.397470));
    map.setZoom(10);
  }

  const value = {
    address: '',
    lng: 0,
    lat: 0
  };

  emit('update:modelValue', value);
  emit('change', value);
};

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue.address) {
    selectedAddress.value = newValue.address;
    addressInput.value = newValue.address;
    if (newValue.lng && newValue.lat) {
      coordinates.value = { lng: newValue.lng, lat: newValue.lat };
      // 如果地图已初始化，更新地图位置
      if (map) {
        updateLocation(newValue.lng, newValue.lat);
        map.setCenter(new TMap.LatLng(newValue.lat, newValue.lng));
      }
    }
  }
}, { immediate: true });

// 组件挂载
onMounted(async () => {
  await nextTick();
  initMap();
});

// 组件卸载
onUnmounted(() => {
  if (map) {
    map.destroy();
  }
  // 清理全局回调
  if ((window as any).onTMapCallback) {
    delete (window as any).onTMapCallback;
  }
});
</script>

<style scoped lang="less">
.map-selector {
  .address-input-section {
    margin-bottom: 16px;

    .input-group {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      flex-wrap: wrap;
      gap: 8px;
    }

    .selected-address {
      font-size: 13px;
      color: #666;
      margin-bottom: 8px;
      padding: 8px 12px;
      background-color: #f6f8fa;
      border-radius: 4px;
      border-left: 3px solid #1890ff;

      .address-label {
        font-weight: 500;
        color: #333;
      }

      .address-text {
        color: #1890ff;
        font-weight: 500;
      }

      .coordinates {
        color: #999;
        font-size: 12px;
        margin-left: 8px;
      }
    }

    .address-tips {
      .tips-text {
        font-size: 12px;
        color: #999;
      }
    }
  }

  .search-results {
    margin-bottom: 16px;

    .results-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 8px;
      color: #333;
    }

    .results-list {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      background: #fff;

      .result-item {
        padding: 12px 16px;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.2s;

        &:hover {
          background-color: #f5f5f5;
          border-left: 3px solid #1890ff;
        }

        &:last-child {
          border-bottom: none;
        }

        .result-name {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin-bottom: 4px;
        }

        .result-address {
          font-size: 12px;
          color: #666;
          line-height: 1.4;
        }
      }
    }
  }

  .map-container {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    #tencent-map {
      width: 100%;
      height: 400px;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .map-selector {
    .address-input-section {
      .input-group {
        flex-direction: column;
        align-items: stretch;

        .ant-input {
          width: 100% !important;
          margin-right: 0 !important;
          margin-bottom: 8px;
        }

        .ant-btn {
          margin-left: 0 !important;
        }
      }
    }

    .map-container {
      #tencent-map {
        height: 300px;
      }
    }
  }
}
</style>
