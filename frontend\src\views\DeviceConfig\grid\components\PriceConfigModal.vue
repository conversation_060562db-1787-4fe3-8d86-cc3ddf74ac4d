<template>
  <a-drawer
    title="电价配置"
    :visible="visible"
    :width="800"
    @close="handleClose"
  >
    <div class="price-config-content">
      <!-- 地区信息 -->
      <div class="region-info">
        <h3>{{ config?.province }}{{ config?.city ? '-' + config?.city : '' }}</h3>
        <p>电价区间: {{ getPeriodText(config?.period) }}</p>
      </div>

      <!-- 月份标签 -->
      <div class="month-tabs">
        <a-tabs v-model:activeKey="activeMonthGroup" @change="handleMonthGroupChange">
          <a-tab-pane 
            v-for="(group, index) in monthGroups" 
            :key="index"
            :tab="group.label"
          >
            <!-- 24小时电价图表 -->
            <div class="price-chart-container">
              <PriceChart 
                :data="group.priceData"
                @select="handleChartSelect"
              />
            </div>

            <!-- 变价按钮 -->
            <div class="price-actions">
              <a-button type="primary" @click="openPriceEditModal(index)">
                变价
              </a-button>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>

    <!-- 电价编辑滑窗 -->
    <a-drawer
      title="电价时段编辑"
      :visible="showPriceEditModal"
      :width="500"
      @close="showPriceEditModal = false"
    >
      <div class="price-edit-content">
        <!-- 时段列表 -->
        <div class="time-periods">
          <div 
            v-for="(period, index) in currentPeriods" 
            :key="index"
            class="period-item"
          >
            <a-card size="small">
              <template #title>
                <div class="period-title">
                  <span>时段 {{ index + 1 }}</span>
                  <a-button 
                    size="small" 
                    danger 
                    type="text"
                    @click="removePeriod(index)"
                  >
                    删除
                  </a-button>
                </div>
              </template>
              
              <a-form :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <a-form-item label="类型">
                  <a-select v-model:value="period.type">
                    <a-select-option :value="5">尖峰</a-select-option>
                    <a-select-option :value="4">高峰</a-select-option>
                    <a-select-option :value="3">平时</a-select-option>
                    <a-select-option :value="2">低谷</a-select-option>
                    <a-select-option :value="1">深谷</a-select-option>
                  </a-select>
                </a-form-item>
                
                <a-form-item label="价格">
                  <a-input-number 
                    v-model:value="period.price"
                    :min="0"
                    :step="0.01"
                    style="width: 100%"
                    addon-after="元/kWh"
                  />
                </a-form-item>
                
                <a-form-item label="时间段">
                  <a-time-range-picker
                    v-model:value="period.timeRange"
                    format="HH:00"
                    :minute-step="60"
                    :hour-step="1"
                    :disabled-hours="() => getDisabledHours(index)"
                    @change="handleTimeChange(index, $event)"
                  />
                </a-form-item>
              </a-form>
            </a-card>
          </div>
        </div>

        <!-- 新增时段 -->
        <a-button 
          type="dashed" 
          block 
          @click="addPeriod"
          style="margin-top: 16px"
        >
          <PlusOutlined />
          新增时段
        </a-button>
      </div>

      <template #footer>
        <a-space>
          <a-button @click="showPriceEditModal = false">取消</a-button>
          <a-button type="primary" @click="savePriceEdit">确认</a-button>
        </a-space>
      </template>
    </a-drawer>

    <template #footer>
      <a-space>
        <a-button @click="handleClose">取消</a-button>
        <a-button type="primary" @click="handleSave">保存</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import PriceChart from './PriceChart.vue'
import dayjs from 'dayjs'

const props = defineProps(['visible', 'config'])
const emit = defineEmits(['update:visible', 'confirm'])

const activeMonthGroup = ref(0)
const showPriceEditModal = ref(false)
const currentPeriods = ref([])
const editingGroupIndex = ref(0)

// 获取周期文本
const getPeriodText = (period) => {
  const periodMap = {
    year: '年(1-12月)',
    halfYear: '半年(上下半年)', 
    quarter: '季度(四个季度)',
    month: '按月(12个月)'
  }
  return periodMap[period] || '未知'
}

// 根据周期生成月份分组
const monthGroups = computed(() => {
  if (!props.config) return []
  
  const { period } = props.config
  
  let groups = []
  
  switch (period) {
    case 'year':
      groups = [{ label: '全年(1-12月)', begin: 1, end: 12, priceData: [] }]
      break
    case 'halfYear':
      groups = [
        { label: '上半年(1-6月)', begin: 1, end: 6, priceData: [] },
        { label: '下半年(7-12月)', begin: 7, end: 12, priceData: [] }
      ]
      break
    case 'quarter':
      groups = [
        { label: '第一季度(1-3月)', begin: 1, end: 3, priceData: [] },
        { label: '第二季度(4-6月)', begin: 4, end: 6, priceData: [] },
        { label: '第三季度(7-9月)', begin: 7, end: 9, priceData: [] },
        { label: '第四季度(10-12月)', begin: 10, end: 12, priceData: [] }
      ]
      break
    case 'month':
      groups = Array.from({ length: 12 }, (_, i) => ({
        label: `${i + 1}月`,
        begin: i + 1,
        end: i + 1,
        priceData: []
      }))
      break
    default:
      groups = []
  }
  
  // 如果有已存在的配置数据，加载它
  if (props.config && props.config.groupedTouPriceByMonth) {
    props.config.groupedTouPriceByMonth.forEach(monthGroup => {
      const matchingGroups = groups.filter(g => 
        g.begin >= monthGroup.begin && g.end <= monthGroup.end
      )
      
      matchingGroups.forEach(group => {
        group.priceData = monthGroup.groupedTouPriceByDay || []
      })
    })
  }
  
  return groups
})

const handleMonthGroupChange = (key) => {
  activeMonthGroup.value = key
}

const handleChartSelect = (data) => {
  console.log('图表选择:', data)
}

const openPriceEditModal = (groupIndex) => {
  editingGroupIndex.value = groupIndex
  const currentGroup = monthGroups.value[groupIndex]
  
  // 转换数据格式用于编辑
  currentPeriods.value = (currentGroup.priceData || []).map(period => ({
    type: period.type,
    price: period.price,
    begin: period.begin,
    end: period.end,
    timeRange: [
      dayjs().hour(period.begin).minute(0),
      dayjs().hour(period.end).minute(0)
    ]
  }))
  
  showPriceEditModal.value = true
}

// 获取已被占用的小时
const getDisabledHours = (currentIndex) => {
  const disabledHours = []
  
  currentPeriods.value.forEach((period, index) => {
    if (index !== currentIndex) {
      for (let hour = period.begin; hour <= period.end; hour++) {
        disabledHours.push(hour)
      }
    }
  })
  
  return disabledHours
}

const addPeriod = () => {
  // 找出未被占用的时间段
  const occupiedHours = new Set()
  
  currentPeriods.value.forEach(period => {
    for (let hour = period.begin; hour <= period.end; hour++) {
      occupiedHours.add(hour)
    }
  })
  
  // 找到第一个可用的开始时间
  let startHour = 0
  while (occupiedHours.has(startHour) && startHour < 24) {
    startHour++
  }
  
  // 找到连续可用的结束时间
  let endHour = startHour
  while (!occupiedHours.has(endHour + 1) && endHour < 23) {
    endHour++
  }
  
  // 如果找不到可用时间，提示用户
  if (startHour >= 24) {
    return
  }
  
  currentPeriods.value.push({
    type: 3, // 默认平时
    price: 0.5,
    begin: startHour,
    end: endHour,
    timeRange: [
      dayjs().hour(startHour).minute(0),
      dayjs().hour(endHour).minute(0)
    ]
  })
}

const removePeriod = (index) => {
  currentPeriods.value.splice(index, 1)
}

const handleTimeChange = (index, timeRange) => {
  if (timeRange && timeRange.length === 2) {
    currentPeriods.value[index].begin = timeRange[0].hour()
    currentPeriods.value[index].end = timeRange[1].hour()
  }
}

const savePriceEdit = () => {
  // 保存当前月份组的电价配置
  const currentGroup = monthGroups.value[editingGroupIndex.value]
  if (currentGroup) {
    currentGroup.priceData = currentPeriods.value.map(period => ({
      begin: period.begin,
      end: period.end,
      price: period.price,
      type: period.type
    }))
  }
  showPriceEditModal.value = false
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleSave = () => {
  const configData = {
    ...props.config,
    groupedTouPriceByMonth: []
  }
  
  // 根据月份分组合并数据
  const groupedByRange = {}
  
  monthGroups.value.forEach(group => {
    const key = `${group.begin}-${group.end}`
    
    if (!groupedByRange[key]) {
      groupedByRange[key] = {
        begin: group.begin,
        end: group.end,
        groupedTouPriceByDay: group.priceData
      }
    }
  })
  
  configData.groupedTouPriceByMonth = Object.values(groupedByRange)
  
  emit('confirm', configData)
}

// 监听配置变化，初始化数据
watch(() => props.config, (newConfig) => {
  if (newConfig) {
    activeMonthGroup.value = 0
  }
}, { immediate: true })
</script>

<style scoped>
.price-config-content {
  height: 100%;
}

.region-info {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.region-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.region-info p {
  margin: 0;
  color: #666;
}

.month-tabs {
  height: calc(100% - 60px);
}

.price-chart-container {
  height: 300px;
  margin-bottom: 20px;
}

.price-actions {
  text-align: center;
}

.price-edit-content {
  max-height: 70vh;
  overflow-y: auto;
}

.period-item {
  margin-bottom: 16px;
}

.period-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
