import{g as x}from"./index-cXoZ52Tw.js";import{_ as v,r as u,o as I,d as r,b as t,c,e as k,f as a,w as e,F as h,i as w,j as o,k as C,p as z,m as B,l as N,t as S}from"./index-Blktg-U-.js";const V=s=>(z("data-v-496fa3b4"),s=s(),B(),s),A={class:"body_wrap"},D=V(()=>k("div",{class:"title1"}," 策略管理 ",-1)),F={class:"content_wrap"},L={key:0},T={key:1},j={__name:"index",setup(s){const _=u([]),d=u(!1),m=[{title:"名称",dataIndex:"name",key:"name"},{title:"状态",dataIndex:"status",key:"status"},{title:"描述",dataIndex:"desc",key:"desc"},{title:"周期",key:"period",dataIndex:"period"},{title:"评分",key:"score",dataIndex:"score"},{title:"定制时间",key:"createAt",dataIndex:"createAt"},{title:"定制人",key:"creator",dataIndex:"creator"},{title:"Action",key:"action"}],y=async()=>{d.value=!0;const{data:i}=await x();_.value=i,d.value=!1,console.log("table: ",i)};return I(()=>{y()}),(i,E)=>{const f=r("a-tag"),n=r("a-button"),b=r("a-table");return t(),c("div",A,[D,k("div",F,[a(b,{loading:d.value,columns:m,"data-source":_.value,pagination:!1},{bodyCell:e(({column:p,record:g})=>[p.key==="tags"?(t(),c("span",L,[(t(!0),c(h,null,w(g.tags,l=>(t(),N(f,{key:l,color:l==="loser"?"volcano":l.length>5?"geekblue":"green"},{default:e(()=>[o(S(l.toUpperCase()),1)]),_:2},1032,["color"]))),128))])):p.key==="action"?(t(),c("span",T,[a(n,{type:"link",size:"small"},{default:e(()=>[o("详情")]),_:1}),a(n,{type:"link",size:"small"},{default:e(()=>[o("暂停/启动")]),_:1}),a(n,{type:"link",size:"small"},{default:e(()=>[o("删除")]),_:1}),a(n,{type:"link",size:"small"},{default:e(()=>[o("配置")]),_:1})])):C("",!0)]),_:1},8,["loading","data-source"])])])}}},U=v(j,[["__scopeId","data-v-496fa3b4"]]);export{U as default};
