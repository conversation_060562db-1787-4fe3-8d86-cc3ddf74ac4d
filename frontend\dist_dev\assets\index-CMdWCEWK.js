import{d as M,u as z,B as P,r as d,b as A,o as G,y as J,g as k,h as v,i as K,j as a,c as B,q as O,w as y,k as n,F as q,n as p,t as m,p as w,_ as Q}from"./index-qMHOHHUH.js";import{c as W}from"./index-BB0yVVhp.js";import{f as X,g as b}from"./index-DsvExlCQ.js";const Y=e=>[{title:e("runRecord.proName"),width:"8%",dataIndex:"proj_name",key:"proj_name"},{title:e("runRecord.proBg"),width:"15%",dataIndex:"proj_background",key:"proj_background"},{title:e("runRecord.custRequire"),width:"15%",dataIndex:"req_desc",key:"req_desc"},{title:e("runRecord.proTask"),width:"100px",dataIndex:"task_type",key:"task_type"},{title:e("runRecord.customName"),dataIndex:"customer",key:"customer"},{title:e("runRecord.operator"),dataIndex:"operator",key:"operator"},{title:e("runRecord.submitTime"),dataIndex:"submit_time",key:"submit_time"},{title:e("runRecord.calcTime"),dataIndex:"calc_time",key:"calc_time"},{title:e("runRecord.status"),dataIndex:"status",key:"status"},{title:e("runRecord.action"),key:"action"}],j=e=>[{label:e("runRecord.statusOptions.processing"),value:1,color:"processing"},{label:e("runRecord.statusOptions.success"),value:2,color:"success"},{label:e("runRecord.statusOptions.error"),value:3,color:"error"}],V=e=>[{label:e("base.gridOff"),value:1,color:"#108ee9"},{label:e("base.gridConnected"),value:2,color:"#87d068"}],Z=e=>Math.round(e/1e3/60*100)/100,$={class:"table_wrap"},ee={key:0,class:"tool_wrap"},ae=M({__name:"index",setup(e){const u=z(),{locale:D}=z(),F=P(),I=d([]),U=A(),f=d(!1),x=d(0),C=d(),t=d({operator:"",pageSize:10,pageNumber:1}),_=async(o=!0)=>{var g;f.value=o;const r={pageSize:t.value.pageSize,pageNumber:t.value.pageNumber};(g=t.value.operator)!=null&&g.trim()&&(r.operator=t.value.operator);const{msg:S,data:i,code:R}=await W(r);f.value=!1,R===0&&(I.value=i.result,x.value=i.total)},E=o=>{console.log("page:",o),t.value.pageNumber=o.current,t.value.pageSize=o.pageSize,_()},H=o=>{const r=U.resolve({name:"runResult",params:{taskId:o.task_id},query:{type:"1",lang:D.value}});window.open(r.href,"_blank")},L=()=>{_()};return G(async()=>{_(),C.value=setInterval(()=>{_(!1)},5*1e3)}),J(()=>{clearTimeout(C.value)}),(o,r)=>{const S=k("a-input-search"),i=k("a-tag"),R=k("a-button"),g=k("a-table");return n(),v("div",null,[K("div",$,[a(F).isAdmin?(n(),v("div",ee,[B(S,{value:t.value.operator,"onUpdate:value":r[0]||(r[0]=s=>t.value.operator=s),placeholder:"输入操作者",style:{width:"200px"},onSearch:L},null,8,["value"])])):O("",!0),B(g,{class:"table_content",columns:a(Y)(a(u).t),"data-source":I.value,loading:f.value,onChange:E,pagination:{pageSize:t.value.pageSize,total:x.value,hideOnSinglePage:!1,showTotal:s=>`Total ${s} items`}},{bodyCell:y(({column:s,record:h,text:l})=>{var T,N;return[s.key==="submit_time"?(n(),v(q,{key:0},[p(m(a(X)(l)),1)],64)):s.key==="status"?(n(),w(i,{key:1,color:(T=a(b)(a(j)(a(u).t),l))==null?void 0:T.color},{default:y(()=>{var c;return[p(m((c=a(b)(a(j)(a(u).t),l))==null?void 0:c.label),1)]}),_:2},1032,["color"])):s.key==="task_type"?(n(),w(i,{key:2,color:(N=a(b)(a(V)(a(u).t),l))==null?void 0:N.color},{default:y(()=>{var c;return[p(m((c=a(b)(a(V)(a(u).t),l))==null?void 0:c.label),1)]}),_:2},1032,["color"])):s.key==="calc_time"?(n(),v(q,{key:3},[p(m(h.status===2?a(Z)(l):"—"),1)],64)):s.key==="action"?(n(),w(R,{key:4,type:"link",onClick:c=>H(h),disabled:h.status==3},{default:y(()=>[p(m(o.$t("runRecord.result")),1)]),_:2},1032,["onClick","disabled"])):O("",!0)]}),_:1},8,["columns","data-source","loading","pagination"])])])}}}),re=Q(ae,[["__scopeId","data-v-c661ee21"],["__file","E:/02 ems code/LCOH/frontend/src/views/RunRecord/index.vue"]]);export{re as default};
