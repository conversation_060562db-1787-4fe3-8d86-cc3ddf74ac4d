import { createI18n } from 'vue-i18n'

// 导入语言文件
import zhCN from './zh-CN'
import enUS from './en-US'

const messages = {
  'zh_CN': zhCN,
  'en_US': enUS
}

// 获取存储的语言设置
const getStoredLocale = () => {
  return localStorage.getItem('lang') || 'zh_CN'
}

// 创建 i18n 实例
const i18n = createI18n({
  legacy: false, // 使用 Composition API
  locale: getStoredLocale(), // 从本地存储获取语言
  fallbackLocale: 'zh_CN', // 回退语言
  messages,
  globalInjection: true // 全局注入 $t 函数
})

// 设置语言切换函数
export const setLocale = (locale: string) => {
  i18n.global.locale.value = locale as any
}

// 获取当前语言
export const getLocale = () => {
  return i18n.global.locale.value
}

export default i18n
