import { type Router } from 'vue-router'
// import { useUserInfo, canAccessModule } from '@/stores/auth'

export const initRouterHook = async (router: Router) => {
  
  router.beforeResolve(async (to) => {
    // const userState = useUserInfo()
    // try {
    //   if (!userState.userInfo.groupInfo) {
    //     await userState.fetchUserInfo()
    //   }
    //   const canAccessPage = canAccessModule(userState.userInfo, to.meta.id as string)
    //   userState.setPagePermission(!!canAccessPage)
    //   console.log('canAccessPage:', userState.userInfo, canAccessPage)

    // } catch (e) {
    //   console.error(e)
    // }
  })
}