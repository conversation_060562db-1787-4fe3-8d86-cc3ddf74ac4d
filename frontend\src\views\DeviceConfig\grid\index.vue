
<template>
  <div class="grid-price-config">
    <div class="main-content">
      <!-- 左侧地区树-->
      <div class="left-panel">
        <div class="panel-header">
          <span class="panel-title">地区</span>
          <a-button type="primary" size="small" @click="showRegionModal = true">
            <PlusOutlined />
            新增
          </a-button>
        </div>
        
        <div class="region-tree">
          <a-tree
            :tree-data="regionTreeData"
            :selected-keys="selectedKeys"
            :expanded-keys="expandedKeys"
            :default-expand-all="true"
            @select="handleTreeSelect"
            @expand="handleTreeExpand"
          >
            <template #title="{ title, key, isLeaf }">
              <div class="tree-node-content">
                <span class="node-title">{{ title }}</span>
                <div class="node-actions" v-if="isLeaf">
                  <a-popconfirm
                    title="确定删除此地区配置吗？"
                    @confirm="deleteRegion(key)"
                    @click.stop
                  >
                    <a-button size="small" type="link" danger>删除</a-button>
                  </a-popconfirm>
                </div>
              </div>
            </template>
          </a-tree>
        </div>
      </div>

      <!-- 右侧配置区域 -->
      <div class="right-panel">
        <div v-if="currentConfig" class="config-area">
          <!-- 地区标题 -->
          <div class="config-header">
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <h3>
                {{ currentConfig.province }}{{ currentConfig.city ? '-' + currentConfig.city : '' }}
                <a-tag v-if="currentConfig.isNew" color="orange" style="margin-left: 8px">
                  未保存
                </a-tag>
              </h3>
              <!-- <a-button
                v-if="currentConfig.isNew || !checkAllMonthGroupsConfigured(currentConfig)"
                type="primary"
                @click="manualSubmitConfig"
                :loading="submitting"
              >
                保存配置
              </a-button> -->
            </div>
            <!-- <p>电价区间: {{ getPeriodText(currentConfig.period) }}</p> -->
          </div>


          
          <!-- 月份标签 -->
          <div class="month-tabs">
            <a-tabs v-model:activeKey="activeMonthGroup" @change="handleMonthGroupChange">
              <a-tab-pane 
                v-for="(group, index) in monthGroups" 
                :key="index"
                :tab="group.label"
              >
                <!-- 24小时电价配置 -->
                <div class="price-config-section">
                  <div class="section-header">
                    <h4>24小时电价配置</h4>
                    <a-button type="primary" size="small" @click="openTimeEditModal">
                      编辑
                    </a-button>
                  </div>
                  
                  <div class="price-chart-container">
                    <PriceChart 
                      ref="priceChartRef"
                      :data="group.priceData"
                      @select="handleChartSelect"
                    />
                  </div>
                </div>
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <a-empty description="请选择左侧地区查看电价配置" />
        </div>
      </div>
    </div>

    <!-- 新增地区弹窗 -->
    <RegionModal
      v-model:visible="showRegionModal"
      :existing-regions="priceConfigs"
      @confirm="handleRegionConfirm"
    />

    <!-- 时间段编辑滑窗 -->
    <a-drawer
      title="电价时段编辑"
      :visible="showTimeEditModal"
      :width="500"
      @close="showTimeEditModal = false"
    >
      <div class="time-edit-content">
        <!-- 时段列表 -->
        <div class="time-periods">
          <div 
            v-for="(period, index) in currentPeriods" 
            :key="index"
            class="period-item"
          >
            <a-card size="small">
              <template #title>
                <div class="period-title">
                  <span>时段 {{ index + 1 }}</span>
                  <a-button 
                    size="small" 
                    danger 
                    type="text"
                    @click="removePeriod(index)"
                  >
                    删除
                  </a-button>
                </div>
              </template>
              
              <a-form :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <a-form-item label="类型">
                  <a-select v-model:value="period.type" @change="syncToChart">
                    <a-select-option :value="5">尖峰</a-select-option>
                    <a-select-option :value="4">高峰</a-select-option>
                    <a-select-option :value="3">平时</a-select-option>
                    <a-select-option :value="2">低谷</a-select-option>
                    <a-select-option :value="1">深谷</a-select-option>
                  </a-select>
                </a-form-item>
                
                <a-form-item label="价格">
                  <a-input-number 
                    v-model:value="period.price"
                    :min="0"
                    :step="0.01"
                    style="width: 100%"
                    addon-after="元/kWh"
                    @change="syncToChart"
                  />
                </a-form-item>
                
                <a-form-item label="时间段">
                  <a-time-range-picker
                    v-model:value="period.timeRange"
                    format="HH:00"
                    :minute-step="60"
                    :hour-step="1"
                    :disabled-hours="() => getDisabledHours(index)"
                    @change="handleTimeChange(index, $event)"
                  />
                </a-form-item>
              </a-form>
            </a-card>
          </div>
        </div>

        <!-- 新增时段 -->
        <a-button 
          type="dashed" 
          block 
          @click="addPeriod"
          style="margin-top: 16px"
        >
          <PlusOutlined />
          新增时段
        </a-button>
      </div>

      <template #footer>
        <a-space>
          <a-button @click="showTimeEditModal = false">取消</a-button>
          <a-button type="primary" @click="saveTimeEdit">确认</a-button>
        </a-space>
      </template>
    </a-drawer>
  </div>
</template>

<script setup>
import dayjs from 'dayjs'
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { getGridPrice, updateGridPrice, delGridPrice } from '@/api/project'
import RegionModal from './components/RegionModal.vue'
import PriceChart from './components/PriceChart.vue'

const showRegionModal = ref(false)
const showTimeEditModal = ref(false)
const priceConfigs = ref([])
const selectedKeys = ref([])
const expandedKeys = ref([])
const activeMonthGroup = ref(0)
const currentPeriods = ref([])
const priceChartRef = ref()
const submitting = ref(false)

// 当前选中的配置
const currentConfig = computed(() => {
  if (selectedKeys.value.length === 0) return null
  const selectedKey = selectedKeys.value[0]
  return priceConfigs.value.find(config => config.id.toString() === selectedKey)
})

// 构建树形数据
const regionTreeData = computed(() => {
  const provinceMap = new Map()
  const customRegions = []

  priceConfigs.value.forEach(config => {
    const { province, city, id } = config

    if (city) {
      // 省市两级 - 标准地区
      if (!provinceMap.has(province)) {
        provinceMap.set(province, {
          title: province,
          key: `province-${province}`,
          children: [],
          selectable: false
        })
      }

      const provinceNode = provinceMap.get(province)
      provinceNode.children.push({
        title: city,
        key: id.toString(),
        isLeaf: true,
        province,
        city
      })
    } else {
      // 自定义地区 - 只显示一级
      customRegions.push({
        title: province,
        key: id.toString(),
        isLeaf: true,
        province,
        city: ''
      })
    }
  })

  // 合并标准地区和自定义地区
  const result = Array.from(provinceMap.values())
  return [...result, ...customRegions]
})

// 月份分组
const monthGroups = computed(() => {
  if (!currentConfig.value) return []

  const { period, groupedTouPriceByMonth } = currentConfig.value

  // 根据period类型构建基础结构
  let baseGroups = []
  switch (period) {
    case 'year':
      baseGroups = [{ label: '全年(1-12月)', months: [1,2,3,4,5,6,7,8,9,10,11,12], begin: 1, end: 12 }]
      break
    case 'halfYear':
      baseGroups = [
        { label: '上半年(1-6月)', months: [1,2,3,4,5,6], begin: 1, end: 6 },
        { label: '下半年(7-12月)', months: [7,8,9,10,11,12], begin: 7, end: 12 }
      ]
      break
    case 'quarter':
      baseGroups = [
        { label: '第一季度(1-3月)', months: [1,2,3], begin: 1, end: 3 },
        { label: '第二季度(4-6月)', months: [4,5,6], begin: 4, end: 6 },
        { label: '第三季度(7-9月)', months: [7,8,9], begin: 7, end: 9 },
        { label: '第四季度(10-12月)', months: [10,11,12], begin: 10, end: 12 }
      ]
      break
    case 'month':
      baseGroups = Array.from({ length: 12 }, (_, i) => ({
        label: `${i + 1}月`,
        months: [i + 1],
        begin: i + 1,
        end: i + 1
      }))
      break
    default:
      return []
  }

  // 将电价数据匹配到对应的月份组
  return baseGroups.map(group => {
    const matchedPriceGroup = groupedTouPriceByMonth?.find(priceGroup =>
      priceGroup.begin === group.begin && priceGroup.end === group.end
    )

    // 确保电价数据格式正确
    const priceData = matchedPriceGroup?.groupedTouPriceByDay || []
    const validPriceData = priceData.map(period => ({
      type: period.type || 3,
      price: period.price || 0,
      begin: period.begin || 0,
      end: period.end || 23
    }))

    return {
      ...group,
      priceData: validPriceData,
      hasData: validPriceData.length > 0
    }
  })
})

// 获取电价区间文本 (暂时保留，可能在其他地方使用)
// const getPeriodText = (period) => {
//   const periodMap = {
//     year: '年(1-12月)',
//     halfYear: '半年(上下半年)',
//     quarter: '季度(四个季度)',
//     month: '按月(12个月)'
//   }
//   return periodMap[period] || period
// }

// 树选择事件
const handleTreeSelect = (keys) => {
  selectedKeys.value = keys
  // 重置到第一个月份组
  activeMonthGroup.value = 0

  // 调试信息：打印选中的配置
  if (keys.length > 0) {
    const selectedConfig = priceConfigs.value.find(config => config.id.toString() === keys[0])
    console.log('选中的地区配置:', selectedConfig)
    if (selectedConfig) {
      console.log('电价数据:', selectedConfig.groupedTouPriceByMonth)
      console.log('推断的period类型:', selectedConfig.period)
    }
  }
}

// 树展开事件
const handleTreeExpand = (keys) => {
  expandedKeys.value = keys
}

// 处理地区确认
const handleRegionConfirm = (regionData) => {
  // 为新建地区生成本地ID（用于前端标识）
  const localId = Date.now()

  const newConfig = {
    ...regionData,
    id: localId, // 前端本地ID
    zoneId: regionData.zoneId, // 后端ID，自定义地区为null
    currentYear: new Date().getFullYear().toString(), // 新增地区使用当前年份
    createTime: Date.now(),
    isNew: true // 标记为新建地区，尚未保存到后端
  }

  // 只在本地添加，不立即提交到后端
  priceConfigs.value.push(newConfig)
  selectedKeys.value = [newConfig.id.toString()]
  showRegionModal.value = false

  // 自动展开对应的省份节点
  if (regionData.city) {
    expandedKeys.value = [...new Set([...expandedKeys.value, `province-${regionData.province}`])]
  }

  message.success('地区添加成功，请配置电价后保存')
}

// 删除地区
const deleteRegion = async (configId) => {
  const { code, data, msg } = await delGridPrice([parseInt(configId)])
  if (code === 0) {
    message.success('删除成功')
    await getConfigedZone()
    // priceConfigs.value = priceConfigs.value.filter(config => config.id.toString() !== configId)
  } else {
    message.error(msg)
  }
}
// 月份组变化
const handleMonthGroupChange = (key) => {
  activeMonthGroup.value = key
}

// 图表选择事件
const handleChartSelect = (data) => {
  console.log('选中图表数据:', data)
}

// 打开时间编辑弹窗
const openTimeEditModal = () => {
  const currentGroup = monthGroups.value[activeMonthGroup.value]
  if (currentGroup && currentGroup.priceData && currentGroup.priceData.length > 0) {
    // 已存在电价数据，加载现有配置
    currentPeriods.value = currentGroup.priceData.map(period => ({
      ...period,
      timeRange: [
        dayjs().hour(period.begin).minute(0),
        dayjs().hour(period.end).minute(0)
      ]
    }))
  } else {
    // 如果没有数据，初始化一个默认时段（覆盖全天）
    currentPeriods.value = [{
      type: 3, // 平时
      price: 0.5,
      begin: 0,
      end: 23,
      timeRange: [
        dayjs().hour(0).minute(0),
        dayjs().hour(23).minute(0)
      ]
    }]
  }
  showTimeEditModal.value = true
}

// 添加时段
const addPeriod = () => {
  // 找出未被占用的时间段
  const occupiedHours = new Set()
  
  currentPeriods.value.forEach(period => {
    for (let hour = period.begin; hour <= period.end; hour++) {
      occupiedHours.add(hour)
    }
  })
  
  // 找到第一个可用的开始时间
  let startHour = 0
  while (occupiedHours.has(startHour) && startHour < 24) {
    startHour++
  }
  
  // 找到连续可用的结束时间
  let endHour = startHour
  while (!occupiedHours.has(endHour + 1) && endHour < 23) {
    endHour++
  }
  
  // 如果找不到可用时间，提示用户
  if (startHour >= 24) {
    message.warning('24小时时段已全部配置完成')
    return
  }
  
  currentPeriods.value.push({
    type: 3, // 默认平时
    price: 0.5,
    begin: startHour,
    end: endHour,
    timeRange: [
      dayjs().hour(startHour).minute(0),
      dayjs().hour(endHour).minute(0)
    ]
  })
}

// 删除时段
const removePeriod = (index) => {
  currentPeriods.value.splice(index, 1)
}

// 处理时间变化 - 添加校验
const handleTimeChange = (index, timeRange) => {
  if (!timeRange || timeRange.length !== 2) return
  
  const newBegin = timeRange[0].hour()
  const newEnd = timeRange[1].hour()
  
  // 校验时间范围
  if (newBegin >= newEnd) {
    message.error('开始时间必须小于结束时间')
    return
  }
  
  // 校验是否与其他时段冲突
  const hasConflict = currentPeriods.value.some((period, i) => {
    if (i === index) return false
    
    return !(newEnd < period.begin || newBegin > period.end)
  })
  
  if (hasConflict) {
    message.error('时间段与其他时段冲突，请重新选择')
    return
  }
  
  // 更新时间
  currentPeriods.value[index].begin = newBegin
  currentPeriods.value[index].end = newEnd
}

// 实时同步到图表
const syncToChart = () => {
  const currentGroup = monthGroups.value[activeMonthGroup.value]
  if (!currentGroup) return
  
  currentGroup.priceData = currentPeriods.value.map(period => ({
    begin: period.begin,
    end: period.end,
    price: period.price,
    type: period.type
  }))
  
  // 强制更新图表
  nextTick(() => {
    if (priceChartRef.value) {
      priceChartRef.value.updateChart()
    }
  })
}

// 监听价格和类型变化，实时同步
watch(currentPeriods, () => {
  syncToChart()
}, { deep: true })

// 保存时间编辑
const saveTimeEdit = async () => {
  // 校验24小时完整性
  const occupiedHours = new Set()

  currentPeriods.value.forEach(period => {
    for (let hour = period.begin; hour <= period.end; hour++) {
      occupiedHours.add(hour)
    }
  })

  const missingHours = []
  for (let hour = 0; hour < 24; hour++) {
    if (!occupiedHours.has(hour)) {
      missingHours.push(hour)
    }
  }

  if (missingHours.length > 0) {
    message.warning(`以下时段未配置: ${missingHours.map(h => h + ':00').join(', ')}`)
    return
  }

  // 保存到当前配置的groupedTouPriceByMonth中
  if (currentConfig.value) {
    const currentGroup = monthGroups.value[activeMonthGroup.value]
    if (currentGroup) {
      // 更新当前月份组的电价数据
      const newPriceData = currentPeriods.value.map(period => ({
        begin: period.begin,
        end: period.end,
        price: period.price,
        type: period.type
      }))

      // 找到对应的groupedTouPriceByMonth项并更新
      const configIndex = priceConfigs.value.findIndex(config => config.id === currentConfig.value.id)
      if (configIndex > -1) {
        const config = priceConfigs.value[configIndex]
        if (!config.groupedTouPriceByMonth) {
          config.groupedTouPriceByMonth = []
        }

        // 查找或创建对应的月份组
        const monthGroupIndex = config.groupedTouPriceByMonth.findIndex(group =>
          group.begin === currentGroup.begin && group.end === currentGroup.end
        )

        if (monthGroupIndex > -1) {
          // 更新现有的月份组
          config.groupedTouPriceByMonth[monthGroupIndex].groupedTouPriceByDay = newPriceData
        } else {
          // 创建新的月份组
          config.groupedTouPriceByMonth.push({
            begin: currentGroup.begin,
            end: currentGroup.end,
            groupedTouPriceByDay: newPriceData
          })
        }

        // 强制更新响应式数据
        priceConfigs.value[configIndex] = { ...config }

        // 检查是否所有月份组都已配置完成
        const shouldSubmit = checkAllMonthGroupsConfigured(config)

        if (shouldSubmit) {
          // 只有在所有月份组都配置完成后才提交数据
          const updateSuccess = await updateConfigedZone(config)
          if (!updateSuccess) {
            return // 如果更新失败，不关闭弹窗
          }

          // 如果是新建地区，保存成功后移除isNew标记
          if (config.isNew) {
            delete config.isNew
            priceConfigs.value[configIndex] = { ...config }
          }

          message.success('电价配置保存成功')
        } else {
          message.success('当前时段配置保存成功，请继续配置其他时段')
        }
      }
    }
  }

  // 强制更新图表
  nextTick(() => {
    if (priceChartRef.value) {
      priceChartRef.value.updateChart()
    }
  })

  showTimeEditModal.value = false
}

// 获取禁用的小时数
const getDisabledHours = (currentIndex) => {
  const disabledHours = []
  
  currentPeriods.value.forEach((period, index) => {
    if (index !== currentIndex) {
      for (let hour = period.begin; hour <= period.end; hour++) {
        disabledHours.push(hour)
      }
    }
  })
  
  return disabledHours
}

// 监听regionTreeData变化，自动展开所有节点
watch(regionTreeData, (newData) => {
  const allKeys = []
  const collectKeys = (nodes) => {
    nodes.forEach(node => {
      if (!node.isLeaf) {
        allKeys.push(node.key)
      }
      if (node.children) {
        collectKeys(node.children)
      }
    })
  }
  collectKeys(newData)
  expandedKeys.value = allKeys

  // 如果还没有选中任何地区，且有数据，则默认选中第一个
  if (selectedKeys.value.length === 0 && priceConfigs.value.length > 0) {
    selectedKeys.value = [priceConfigs.value[0].id.toString()]
    console.log('树形数据更新后默认选中第一个地区:', priceConfigs.value[0])
  }
}, { immediate: true })

const getConfigedZone = async() => {
  try {
    const { code, msg, data } = await getGridPrice({}) // 传递空对象作为参数
    if (code === 0) {
      // 转换新数据结构到当前组件使用的结构
      priceConfigs.value = data.result.map(item => {
        const { zone, price } = item
        // 获取第一个年份的电价数据（不一定是当前年份）
        const availableYears = Object.keys(price)
        const firstYear = availableYears.length > 0 ? availableYears[0] : new Date().getFullYear().toString()
        const yearPriceData = price[firstYear] || []

        return {
          id: zone.id,
          zoneId: zone.id,
          region: zone.region,
          country: zone.country,
          province: zone.province,
          city: zone.city,
          currentYear: firstYear, // 保存当前使用的年份
          period: determinePeriodFromData(yearPriceData), // 根据数据推断period类型
          createTime: Date.now(),
          groupedTouPriceByMonth: yearPriceData.map(monthGroup => ({
            begin: monthGroup.begin,
            end: monthGroup.end,
            groupedTouPriceByDay: monthGroup.groupedTouPriceByDay
          }))
        }
      })
      console.log('转换后的数据:', priceConfigs.value)

      // 默认选中第一个地区
      if (priceConfigs.value.length > 0) {
        selectedKeys.value = [priceConfigs.value[0].id.toString()]
        console.log('默认选中第一个地区:', priceConfigs.value[0])
      }
    } else {
      message.error(msg)
    }
  } catch (error) {
    console.error('获取电价配置失败:', error)
    message.error('获取电价配置失败')
  }
}

const updateConfigedZone = async(configData) => {
  try {
    // 转换当前组件数据结构到新的API格式
    // 使用配置数据中保存的年份，如果没有则使用当前年份
    const targetYear = configData.currentYear || new Date().getFullYear().toString()
    const submitData = {
      zone: {
        id: configData.zoneId, // 自定义地区为null，标准地区有具体ID
        region: configData.region,
        country: configData.country,
        province: configData.province,
        city: configData.city
      },
      price: {
        [targetYear]: configData.groupedTouPriceByMonth.map(monthGroup => ({
          begin: monthGroup.begin,
          end: monthGroup.end,
          groupedTouPriceByDay: monthGroup.groupedTouPriceByDay
        }))
      }
    }

    console.log('提交的数据:', submitData)

    const { code, msg } = await updateGridPrice(submitData)
    if (code === 0) {
      message.success('电价配置更新成功')
      return true
    } else {
      message.error(msg || '更新失败')
      return false
    }
  } catch (error) {
    console.error('更新电价配置失败:', error)
    message.error('更新失败')
    return false
  }
}

// 根据数据推断period类型的辅助函数
const determinePeriodFromData = (yearPriceData) => {
  if (!yearPriceData || yearPriceData.length === 0) return 'quarter'

  // 检查是否为整年数据（1-12月）
  if (yearPriceData.length === 1) {
    const group = yearPriceData[0]
    if (group.begin === 1 && group.end === 12) return 'year'
  }

  // 检查是否为半年数据（1-6月，7-12月）
  if (yearPriceData.length === 2) {
    const hasFirstHalf = yearPriceData.some(group => group.begin === 1 && group.end === 6)
    const hasSecondHalf = yearPriceData.some(group => group.begin === 7 && group.end === 12)
    if (hasFirstHalf && hasSecondHalf) return 'halfYear'
  }

  // 检查是否为季度数据（1-3，4-6，7-9，10-12月）
  if (yearPriceData.length === 4) {
    const expectedQuarters = [
      { begin: 1, end: 3 },
      { begin: 4, end: 6 },
      { begin: 7, end: 9 },
      { begin: 10, end: 12 }
    ]
    const hasAllQuarters = expectedQuarters.every(quarter =>
      yearPriceData.some(group => group.begin === quarter.begin && group.end === quarter.end)
    )
    if (hasAllQuarters) return 'quarter'
  }

  // 检查是否为月度数据（12个月）
  if (yearPriceData.length === 12) {
    const hasAllMonths = Array.from({ length: 12 }, (_, i) => i + 1).every(month =>
      yearPriceData.some(group => group.begin === month && group.end === month)
    )
    if (hasAllMonths) return 'month'
  }

  return 'quarter' // 默认值
}

// 检查所有月份组是否都已配置完成
const checkAllMonthGroupsConfigured = (config) => {
  if (!config || !config.period) return false

  const { period, groupedTouPriceByMonth } = config

  // 根据period类型确定需要的月份组
  let requiredGroups = []
  switch (period) {
    case 'year':
      requiredGroups = [{ begin: 1, end: 12 }]
      break
    case 'halfYear':
      requiredGroups = [
        { begin: 1, end: 6 },
        { begin: 7, end: 12 }
      ]
      break
    case 'quarter':
      requiredGroups = [
        { begin: 1, end: 3 },
        { begin: 4, end: 6 },
        { begin: 7, end: 9 },
        { begin: 10, end: 12 }
      ]
      break
    case 'month':
      requiredGroups = Array.from({ length: 12 }, (_, i) => ({
        begin: i + 1,
        end: i + 1
      }))
      break
    default:
      return false
  }

  // 检查是否所有必需的月份组都已配置且有电价数据
  return requiredGroups.every(required => {
    const found = groupedTouPriceByMonth?.find(group =>
      group.begin === required.begin && group.end === required.end
    )
    return found && found.groupedTouPriceByDay && found.groupedTouPriceByDay.length > 0
  })
}

// 手动提交配置
const manualSubmitConfig = async () => {
  if (!currentConfig.value) return

  submitting.value = true
  try {
    const updateSuccess = await updateConfigedZone(currentConfig.value)
    if (updateSuccess) {
      // 如果是新建地区，保存成功后移除isNew标记
      if (currentConfig.value.isNew) {
        const configIndex = priceConfigs.value.findIndex(config => config.id === currentConfig.value.id)
        if (configIndex > -1) {
          delete priceConfigs.value[configIndex].isNew
          priceConfigs.value[configIndex] = { ...priceConfigs.value[configIndex] }
        }
      }
      message.success('配置保存成功')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    message.error('保存配置失败')
  } finally {
    submitting.value = false
  }
}

onMounted(async () => {
  await getConfigedZone()

  // 如果没有数据，添加一些测试数据来验证功能
  if (priceConfigs.value.length === 0) {
    console.log('没有获取到数据，添加测试数据')
    // 这里可以添加一些测试数据来验证功能
    // 实际使用时应该删除这部分代码
  }
})
</script>

<style scoped>
.grid-price-config {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.main-content {
  display: flex;
  height: 100%;
  gap: 16px;
}

.left-panel {
  width: 300px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #d9d9d9;
  background: #fafafa;
}

.panel-title {
  font-weight: 500;
}

.region-tree {
  padding: 8px;
  height: calc(100% - 60px);
  overflow-y: auto;
}

.tree-node-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.node-actions {
  display: flex;
  gap: 4px;
}

.right-panel {
  flex: 1;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
}

.config-header {
  margin-bottom: 24px;
}

.config-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}

.config-header p {
  margin: 0;
  color: #666;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
}

.price-chart-container {
  height: 400px;
  width: 100%;
  min-width: 0; /* 允许容器收缩 */
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.time-edit-content {
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.period-item {
  margin-bottom: 16px;
}

.period-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>

