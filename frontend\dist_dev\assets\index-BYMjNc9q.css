.nav[data-v-eef40c4c]{display:flex;justify-content:space-between;align-items:center;padding:10px 15px;background:#09121a}.nav .left[data-v-eef40c4c]{display:flex;color:#fff;font-size:16px}.nav .left[data-v-eef40c4c]:hover{cursor:pointer}.nav .left .logo[data-v-eef40c4c]{width:25px;margin-right:10px;color:#15a675;font-size:26px}.nav .right[data-v-eef40c4c]{display:flex;align-items:center;color:#fff;font-size:14px}.nav .right .face_wrap[data-v-eef40c4c]{margin-right:12px}.nav .right .doc_wrap[data-v-eef40c4c]{margin-right:20px}.nav .right .doc_wrap[data-v-eef40c4c]:hover{cursor:pointer}.nav .right .rate_wrap[data-v-eef40c4c]{display:flex;align-items:center;margin-right:20px}.nav .right .rate_wrap[data-v-eef40c4c]:hover{cursor:pointer}.nav .right .name_wrap[data-v-eef40c4c]{margin:0 5px 0 0}.nav .right .drop_wrap[data-v-eef40c4c]{margin-right:24px;color:#fff}.nav .right .drop_wrap[data-v-eef40c4c]:hover{cursor:pointer}.curreny_form_wrap .currency_form_item[data-v-eef40c4c]{display:flex;margin-top:12px;align-items:center}.curreny_form_wrap .currency_form_item .c_key[data-v-eef40c4c]{margin-right:12px}.curreny_form_wrap .currency_form_item .c_val[data-v-eef40c4c]{display:flex;width:125px}#components-layout-demo-side .logo[data-v-d2c4c435]{height:32px;margin:16px;background:#ffffff4d}.site-layout .site-layout-background[data-v-d2c4c435]{background:#fff}#components-layout-demo-side .logo[data-v-48491176]{height:32px;margin:16px;background:#ffffff4d}.site-layout .site-layout-background[data-v-48491176]{background:#fff}/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */html{line-height:1.15;-webkit-text-size-adjust:100%}body{margin:0}main{display:block}h1{font-size:2em;margin:.67em 0}hr{box-sizing:content-box;height:0;overflow:visible}pre{font-family:monospace,monospace;font-size:1em}a{background-color:transparent}abbr[title]{border-bottom:none;text-decoration:underline;text-decoration:underline dotted}b,strong{font-weight:bolder}code,kbd,samp{font-family:monospace,monospace;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}img{border-style:none}button,input,optgroup,select,textarea{font-family:inherit;font-size:100%;line-height:1.15;margin:0}button,input{overflow:visible}button,select{text-transform:none}button,[type=button],[type=reset],[type=submit]{-webkit-appearance:button}button::-moz-focus-inner,[type=button]::-moz-focus-inner,[type=reset]::-moz-focus-inner,[type=submit]::-moz-focus-inner{border-style:none;padding:0}button:-moz-focusring,[type=button]:-moz-focusring,[type=reset]:-moz-focusring,[type=submit]:-moz-focusring{outline:1px dotted ButtonText}fieldset{padding:.35em .75em .625em}legend{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress{vertical-align:baseline}textarea{overflow:auto}[type=checkbox],[type=radio]{box-sizing:border-box;padding:0}[type=number]::-webkit-inner-spin-button,[type=number]::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}[type=search]::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details{display:block}summary{display:list-item}template{display:none}[hidden]{display:none}:root{--bg-color: #fff;--text-color: #2c3e50}:root[theme=dark]{--bg-color: #0d1117;--text-color: #f0f6fc}
