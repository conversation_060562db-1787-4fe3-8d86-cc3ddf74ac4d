// 导入基本配置
export {
  formBasicInfo,
  formOperationConfig,
  formLaborCostConfig,
  formLandLeaseConfig,
  formFinancingConfig,
  formTaxConfig,
  formOtherConfig
} from './basicConfig'

// 导入光伏配置
import {
  pvInvestmentConfig,
  pvCostConfig,
  pvProfitConfig,
  pvInvestmentAuxConfig,
  pvCostExtendConfig,
  pvProfitExtendConfig
} from './pvConfig'

// 导入风机配置
import {
  windInvestmentConfig,
  windCostConfig,
  windProfitConfig,
  windInvestmentAuxConfig,
  windCostExtendConfig,
  windProfitExtendConfig
} from './windConfig'

// 导入电网配置
import { gridConfig } from './gridConfig'

// 导入储能制氢储氢配置
import {
  storageInvestmentConfig,
  storageCostConfig,
  storageProfitConfig,
  storageInvestmentAuxConfig,
  storageCostExtendConfig,
  storageProfitExtendConfig,
  hydrogenInvestmentConfig,
  hydrogenCostConfig,
  hydrogenProfitConfig,
  hydrogenInvestmentAuxConfig,
  hydrogenCostExtendConfig,
  hydrogenProfitExtendConfig,
  hydrogenStorageInvestmentConfig,
  hydrogenStorageCostConfig,
  hydrogenStorageProfitConfig,
  hydrogenStorageInvestmentAuxConfig,
  hydrogenStorageCostExtendConfig
} from './storageHydrogenConfig'

// 获取资源配置
export const getResourceConfig = (module, section) => {
  if (module === 'pv') {
    switch (section) {
      case 'investment':
        return pvInvestmentConfig
      case 'cost':
        return pvCostConfig
      case 'profit':
        return pvProfitConfig
      default:
        return []
    }
  } else if (module === 'wind') {
    switch (section) {
      case 'investment':
        return windInvestmentConfig
      case 'cost':
        return windCostConfig
      case 'profit':
        return windProfitConfig
      default:
        return []
    }
  } else if (module === 'grid') {
    switch (section) {
      case 'config':
        return gridConfig
      default:
        return []
    }
  } else if (module === 'storage') {
    switch (section) {
      case 'investment':
        return storageInvestmentConfig
      case 'cost':
        return storageCostConfig
      case 'profit':
        return storageProfitConfig
      default:
        return []
    }
  } else if (module === 'hydrogen') {
    switch (section) {
      case 'investment':
        return hydrogenInvestmentConfig
      case 'cost':
        return hydrogenCostConfig
      case 'profit':
        return hydrogenProfitConfig
      default:
        return []
    }
  } else if (module === 'hydrogenStorage') {
    switch (section) {
      case 'investment':
        return hydrogenStorageInvestmentConfig
      case 'cost':
        return hydrogenStorageCostConfig
      case 'profit':
        return hydrogenStorageProfitConfig
      default:
        return []
    }
  }
  return []
}

// 获取辅助计算配置
export const getAuxCalculateConfig = (module, section) => {
  if (module === 'pv' && section === 'investment') {
    return pvInvestmentAuxConfig
  } else if (module === 'wind' && section === 'investment') {
    return windInvestmentAuxConfig
  } else if (module === 'storage' && section === 'investment') {
    return storageInvestmentAuxConfig
  } else if (module === 'hydrogen' && section === 'investment') {
    return hydrogenInvestmentAuxConfig
  } else if (module === 'hydrogenStorage' && section === 'investment') {
    return hydrogenStorageInvestmentAuxConfig
  }
  return []
}

// 获取扩展参数配置
export const getExtendConfig = (module, section) => {
  if (module === 'pv') {
    switch (section) {
      case 'cost':
        return pvCostExtendConfig
      case 'profit':
        return pvProfitExtendConfig
      default:
        return []
    }
  } else if (module === 'wind') {
    switch (section) {
      case 'cost':
        return windCostExtendConfig
      case 'profit':
        return windProfitExtendConfig
      default:
        return []
    }
  } else if (module === 'storage') {
    switch (section) {
      case 'cost':
        return storageCostExtendConfig
      case 'profit':
        return storageProfitExtendConfig
      default:
        return []
    }
  } else if (module === 'hydrogen') {
    switch (section) {
      case 'cost':
        return hydrogenCostExtendConfig
      case 'profit':
        return hydrogenProfitExtendConfig
      default:
        return []
    }
  } else if (module === 'hydrogenStorage') {
    switch (section) {
      case 'cost':
        return hydrogenStorageCostExtendConfig
      default:
        return []
    }
  }
  return []
} 