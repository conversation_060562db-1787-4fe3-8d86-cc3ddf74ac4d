<template>
  <div class="body_wrap">
    <div class="title1">
      策略管理
    </div>
    <div class="content_wrap">
      <a-table
        :loading="tableLoading"
        :columns="columns" :data-source="tableData" :pagination="false"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'tags'">
            <span>
              <a-tag
                v-for="tag in record.tags"
                :key="tag"
                :color="tag === 'loser' ? 'volcano' : tag.length > 5 ? 'geekblue' : 'green'"
              >
                {{ tag.toUpperCase() }}
              </a-tag>
            </span>
          </template>
          <template v-else-if="column.key === 'action'">
            <span>
              <a-button type="link" size="small">详情</a-button>
              <a-button type="link" size="small">暂停/启动</a-button>
              <a-button type="link" size="small">删除</a-button>
              <a-button type="link" size="small">配置</a-button>
            </span>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>
<script setup>
import { getPolicy } from '@/api/strategy'
import { onMounted, ref } from 'vue'

const tableData = ref([])
const tableLoading = ref(false)
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '描述',
    dataIndex: 'desc',
    key: 'desc',
  },
  {
    title: '周期',
    key: 'period',
    dataIndex: 'period',
  },
  {
    title: '评分',
    key: 'score',
    dataIndex: 'score',
  },
  {
    title: '定制时间',
    key: 'createAt',
    dataIndex: 'createAt',
  },
  {
    title: '定制人',
    key: 'creator',
    dataIndex: 'creator',
  },
  {
    title: 'Action',
    key: 'action',
  },
];

const getTableData = async() => {
  tableLoading.value = true
  const { data } = await getPolicy()
  tableData.value = data
  tableLoading.value = false

  console.log('table: ', data)
}
onMounted(() => {
  getTableData()
})

</script>

<style lang="less" scoped>
@import '@/style/base.less';
.content_wrap {
  margin: 10px 0 0 0;
}
.body_wrap {
  padding: 10px 20px;
}
</style>