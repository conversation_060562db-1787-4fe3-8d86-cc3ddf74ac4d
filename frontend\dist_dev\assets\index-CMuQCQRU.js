import{d as L,r as N,a as S,b as q,u as B,s as j,e as E,g as t,h as U,i as r,t as d,c as s,w as a,J as V,m as x,k as I,n as v,j as O,K as R,_ as T}from"./index-qMHOHHUH.js";const z={class:"bg_wrap"},D={class:"login_window"},F={class:"title"},G={class:"lang_title_wrap"},H=L({__name:"index",setup(J){const i=N(!1),w=S(),m=q(),{locale:_}=B(),c=j(),n=E({username:"",password:""}),b=async e=>{i.value=!0;const{code:o,msg:p}=await V(e);i.value=!1,o===0?(console.log("login success"),m.push({name:"home"})):x.error(p)},f=e=>{w.setLang(e),console.log("lang:",e),_.value=e,m.push({...c,query:{...c.query,lang:_.value}})};return(e,o)=>{const p=t("a-input"),u=t("a-form-item"),h=t("a-input-password"),y=t("a-button"),g=t("a-menu-item"),$=t("a-menu"),C=t("a-dropdown"),k=t("a-form");return I(),U("div",z,[r("div",D,[r("div",F,d(e.$t("login.title")),1),s(k,{class:"form_wrap",model:n,name:"basic","label-col":{span:6},"wrapper-col":{span:14},autocomplete:"off",onFinish:b},{default:a(()=>[s(u,{label:e.$t("login.username"),name:"username",rules:[{required:!0,message:e.$t("login.tipsName")}]},{default:a(()=>[s(p,{value:n.username,"onUpdate:value":o[0]||(o[0]=l=>n.username=l)},null,8,["value"])]),_:1},8,["label","rules"]),s(u,{label:e.$t("login.password"),name:"password",rules:[{required:!0,message:e.$t("login.tipsPassword")}]},{default:a(()=>[s(h,{value:n.password,"onUpdate:value":o[1]||(o[1]=l=>n.password=l)},null,8,["value"])]),_:1},8,["label","rules"]),s(u,{"wrapper-col":{offset:6,span:14}},{default:a(()=>[s(y,{loading:i.value,style:{width:"100%"},type:"primary","html-type":"submit"},{default:a(()=>[v(d(e.$t("login.login")),1)]),_:1},8,["loading"])]),_:1}),s(u,{"wrapper-col":{offset:6,span:14}},{default:a(()=>[s(C,{class:"drop_wrap"},{overlay:a(()=>[s($,{style:{width:"100px"}},{default:a(()=>[s(g,null,{default:a(()=>[r("a",{href:"javascript:;",onClick:o[2]||(o[2]=l=>f("zh_CN"))},"中文")]),_:1}),s(g,null,{default:a(()=>[r("a",{href:"javascript:;",onClick:o[3]||(o[3]=l=>f("en_US"))},"English")]),_:1})]),_:1})]),default:a(()=>[r("div",G,[s(O(R)),v(" "+d(e.$t("langTitle")),1)])]),_:1})]),_:1})]),_:1},8,["model"])])])}}}),P=T(H,[["__scopeId","data-v-374a0b31"],["__file","E:/02 ems code/LCOH/frontend/src/views/Login/index.vue"]]);export{P as default};
