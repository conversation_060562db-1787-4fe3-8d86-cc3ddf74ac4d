<template>
  <div class="detail-indicators">
    <h2 class="section-title">详情指标</h2>
    
    <!-- 将所有表格平铺显示 -->
    <div 
      v-for="config in tableConfigs" 
      :key="config.key"
      class="table-section"
    >
      <h3 class="table-title">{{ config.tab }}</h3>
      
      <!-- 非年份表格 -->
      <a-table
        v-if="config.type !== 'yearly'"
        :columns="getTableColumns(config)"
        :data-source="getTableData(config)"
        :pagination="false"
        size="small"
        bordered
      />
      
      <!-- 年份表格 - 分段显示 -->
      <div v-else class="yearly-tables">
        <!-- 第一部分：前13年 -->
        <div class="yearly-table-part">
          <h4 class="yearly-table-subtitle">第1-13年</h4>
          <a-table
            :columns="getYearlyTableColumns(config, 1, 13)"
            :data-source="getYearlyTableData(config, 1, 13)"
            :pagination="false"
            size="small"
            bordered
          />
        </div>
        
        <!-- 第二部分：后续年份 -->
        <div class="yearly-table-part" v-if="yearCount > 13">
          <h4 class="yearly-table-subtitle">第14-{{ yearCount }}年</h4>
          <a-table
            :columns="getYearlyTableColumns(config, 14, yearCount)"
            :data-source="getYearlyTableData(config, 14, yearCount)"
            :pagination="false"
            size="small"
            bordered
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import {
  tableColumns,
  generateYearColumns,
  tableDataConfigs,
  yearlyTableConfigs,
  generateYearlyTableData,
  generateBasicTableData,
  formatNumber,
  formatPercentage,
  formatDecimal
} from '../../EconomicDetail/util.js'

// 接收父组件传递的数据
const props = defineProps({
  resultData: {
    type: Object,
    default: () => null
  }
})

// 统一获取运营年份数量
const operatingYears = computed(() => {
  return props.resultData?.financialIndicatorsSummary?.operatingYears || 25
})

// 年份数量（运营年份+1，包含第0年建设期）
const yearCount = computed(() => {
  return operatingYears.value + 1
})

// 表格配置数组 - 数据驱动渲染
const tableConfigs = [
  {
    key: '2',
    tab: '固定资产投资估算',
    type: 'basic',
    dataKey: 'fixedAssetsInvestmentEstimation',
    configKey: 'fixedAssets'
  },
  {
    key: '3',
    tab: '工程总概算',
    type: 'basic',
    dataKey: 'projectOverallBudget',
    configKey: 'projectBudget'
  },
  {
    key: '4',
    tab: '融资计划',
    type: 'basic',
    dataKey: 'financingPlan',
    configKey: 'financing'
  },
  {
    key: '5',
    tab: '投资计划与资金筹措',
    type: 'basic',
    dataKey: 'investmentPlanAndFundRaising',
    configKey: 'investmentPlan'
  },
  {
    key: '6',
    tab: '年度制氢及上网电量',
    type: 'yearly',
    dataKey: 'annualHydrogenAndGridPower',
    configKey: 'annualHydrogenAndGridPower'
  },
  {
    key: '7',
    tab: '还本付息计算',
    type: 'yearly',
    dataKey: 'loanRepaymentSchedule',
    configKey: 'loanRepaymentSchedule'
  },
  {
    key: '8',
    tab: '总成本费用',
    type: 'yearly',
    dataKey: 'totalCostAndExpenses',
    configKey: 'totalCostAndExpenses'
  },
  {
    key: '9',
    tab: '利润和利润分配',
    type: 'yearly',
    dataKey: 'profitAndProfitDistribution',
    configKey: 'profitAndProfitDistribution'
  },
  {
    key: '10',
    tab: '项目投资现金流量',
    type: 'yearly',
    dataKey: 'projectInvestmentCashFlow',
    configKey: 'projectInvestmentCashFlow'
  },
  {
    key: '11',
    tab: '资本金财务现金流量',
    type: 'yearly',
    dataKey: 'equityCapitalCashFlow',
    configKey: 'equityCapitalCashFlow'
  },
  {
    key: '12',
    tab: '财务计划现金流量',
    type: 'yearly',
    dataKey: 'financialPlanCashFlow',
    configKey: 'financialPlanCashFlow'
  },
  {
    key: '13',
    tab: '资产负债',
    type: 'yearly',
    dataKey: 'balanceSheet',
    configKey: 'balanceSheet'
  },
  {
    key: '14',
    tab: '财务指标汇总',
    type: 'basic',
    dataKey: 'financialIndicatorsSummary',
    configKey: 'financialSummary'
  }
]

// 动态生成表格数据
const getTableColumns = (config) => {
  if (config.type === 'yearly') {
    return generateYearColumns(yearCount.value)
  } else {
    return tableColumns[config.configKey]
  }
}

const getTableData = (config) => {
  if (!props.resultData?.[config.dataKey]) return []
  
  if (config.type === 'yearly') {
    return generateYearlyTableData(
      props.resultData[config.dataKey], 
      yearlyTableConfigs[config.configKey], 
      yearCount.value
    )
  } else {
    return generateBasicTableData(
      props.resultData[config.dataKey], 
      tableDataConfigs[config.configKey]
    )
  }
}

// 生成分段年份表格的列
const getYearlyTableColumns = (config, startYear, endYear) => {
  const columns = [
    { title: '指标', dataIndex: 'indicator', key: 'indicator', fixed: 'left', width: 200 }
  ]
  
  // 动态添加年份列
  for (let i = startYear; i <= endYear; i++) {
    columns.push({
      title: `第${i}年`,
      dataIndex: `year${i}`,
      key: `year${i}`,
      width: 100
    })
  }
  
  return columns
}

// 生成分段年份表格的数据
const getYearlyTableData = (config, startYear, endYear) => {
  if (!props.resultData?.[config.dataKey]) return []
  
  const data = props.resultData[config.dataKey]
  const tableConfig = yearlyTableConfigs[config.configKey]
  
  return tableConfig.map((indicator, index) => {
    const row = {
      key: index + 1,
      indicator: indicator.name
    }
    
    for (let i = startYear; i <= endYear; i++) {
      const yearIndex = i - 1
      let value = '--'
      
      if (indicator.isFixed) {
        // 固定值，所有年份相同
        value = data[indicator.key] || '--'
      } else if (data[indicator.key] && data[indicator.key][yearIndex] !== undefined) {
        const rawValue = data[indicator.key][yearIndex]
        if (indicator.isPercentage) {
          value = formatPercentage(rawValue) + '%'
        } else if (indicator.isDecimal) {
          value = formatDecimal(rawValue)
        } else {
          value = formatNumber(rawValue)
        }
      }
      
      row[`year${i}`] = value
    }
    
    return row
  })
}
</script>

<style scoped>
.detail-indicators {
  background: white;
  padding: 16px;
  border-radius: 4px;
}

.section-title {
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 12px;
  color: #333;
}

.table-section {
  margin-bottom: 32px;
}

.table-section:last-child {
  margin-bottom: 0;
}

.table-title {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
  padding: 8px 12px;
  background: #fafafa;
  border-left: 3px solid #1890ff;
  border-radius: 2px;
}

.yearly-tables {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.yearly-table-part {
  width: 100%;
}

.yearly-table-subtitle {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #666;
  padding: 4px 8px;
  background: #f8f9fa;
  border-left: 2px solid #52c41a;
  border-radius: 1px;
}

:deep(.ant-table) {
  font-size: 12px;
  margin-bottom: 16px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 500;
  color: #333;
  padding: 8px;
  font-size: 12px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}
</style> 