
import { createApp } from 'vue'
import { create<PERSON><PERSON> } from 'pinia'
import {
  Layout, LayoutHeader, LayoutContent, LayoutSider, Button, message, Modal, ConfigProvider, Pagination,
  Menu, Tabs, Table, Input, Form, Select, Popconfirm, Typography,
  Spin, Upload, Tooltip, InputNumber, Radio, RadioGroup, DatePicker, TimePicker,
  Affix, Tree, Cascader, Drawer, Tag, Breadcrumb, Card, Dropdown, Avatar,
  Popover, Empty, Checkbox, Switch, Result, Collapse, AutoComplete, Space
} from 'ant-design-vue'
import App from './App.vue'
import router from './router'
import i18n from './lang'
import 'normalize.css/normalize.css'

import './style/variable.css'

const app = createApp(App)

const antComponents = [
  Layout, Button, Modal, ConfigProvider, Pagination, 
  Menu, Tabs, Table, Input, Form, Select, Popconfirm, Typography,
  Spin, Upload, Tooltip, InputNumber, Radio, RadioGroup, DatePicker, TimePicker,
  Affix, Tree, Cascader, Drawer, Tag, Breadcrumb, Card, Dropdown,
  Avatar, Popover, Empty, Checkbox, Switch, Result, Collapse, AutoComplete, Space
];

antComponents.forEach((com: any) => app.use(com))

app.use(createPinia())
app.use(router)
app.use(i18n)

app.mount('#app')
