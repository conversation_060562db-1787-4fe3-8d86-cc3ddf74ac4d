<template>
  <a-modal
    width="90%"
    v-model:open="visible" :title="props.config.modalTitle"
    @ok="handleOk"
  >
    <div class="power_list">
      <div
        v-if="innerData?.length"
        class="p_item" v-for="(item) in innerData"
        @click="selectItem(item)"
        :class="{p_item_sel: item.id === curSel.id}"
      >
        <line-chart :data="item" class="chart_wrap" />
        <div class="title">{{ item.desc }}</div>
      </div>
      <div
        v-if="props.config.canUploadSelf"
        class="p_item" @click="selectItem(selfData)"
        :class="{p_item_sel:  curSel.id === -10}"
      >
        <line-chart :data="selfData" class="chart_wrap" />
        <div class="title">
          <!-- <a-upload
            @change="uploadChange"
            :before-upload="file=>{ formState.pvPower = [file]; return false; }"
            @remove="() => { formState.pvPower = [] }"
            accept=".txt"
            :maxCount="1"
            >
            <a-button size="small" style="fontSize: 11px;marginRight:10px;">
              <template #icon><UploadOutlined /></template>
              上传+
            </a-button>
          </a-upload> -->
          <a-upload
            @change2="uploadChange"
            :before-upload2="file=>beforeUpload(file)"
            :customRequest="customRequest"
            @remove="() => { formState.pvPower = [] }"
            accept=".txt"
            :maxCount="1"
            >
            <a-button size="small" style="fontSize: 11px;marginRight:10px;">
              <template #icon><UploadOutlined /></template>
              上传数据
            </a-button>
          </a-upload>
          自定义数据
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import LineChart from '@/components/LineChart/index.vue'
import { getForecast } from '@/api/project'
import { getXDataFromZero } from '@/util/index'
import { cloneDeep } from 'lodash'
import { baseConfig } from '@/config/baseConfig'

const props = defineProps(['selfData', 'chartList', 'config'])
const zoomRange = ref([40, 50])

const formState = ref({
  pvPower: []
})
const value = ref(1);
const curSel = ref({})
const chartDataList = ref([])
const uploadData = ref([])

const selfData = ref({
  desc: `自定义地区`,
  id: -10,
  title: '',
  series: [
    {
      data: props.selfData,
      type: 'line',
      lineStyle: {
        normal: {
          color: baseConfig.baseColor, //  #15a675 #005236 #1677ff
        }
      },
    }
  ],
  grid: {
    top: '5%',    // 调整顶部留白
    bottom: '10%', // 调整底部留白
    left: '3%',
    right: '5%',
    containLabel: true
  },
  dataZoom: props.config.hasZoom ? [
    {
      show: true,
      realtime: true,
      start: zoomRange.value[0],
      end: zoomRange.value[1],
      // xAxisIndex: [0, 1]
      bottom: '0px'
    },
    {
      type: 'inside',
    }
  ] : null
})
const copySelfData = cloneDeep(selfData.value)
const visible = ref(false)
const emit = defineEmits(['submit'])
const selectItem = (item) => {
  curSel.value = item
}
// const innerData = ref([])
const open = () => {
  visible.value = true
}
const close = () => {
  visible.value = false
}
defineExpose({
  open,
  close
})
const handleOk = () => {
  // console.log('formState.pvPower:', formState.value.pvPower)
  emit('submit', curSel.value)
}
watch(() => props.chartList, (val) => {
 
  // console.log('inner props:', val, innerData.value)
})
const innerData = computed(() => {
  curSel.value = props.config.default || {}
  return props.chartList?.map(i => {
    const { yData, unit, desc, id } = i
    return {
      desc,
      id,
      title: '',
      series: [
        {
          data: yData,
          type: 'line',
          lineStyle: {
            normal: {
              color: baseConfig.baseColor, //  #15a675 #005236 #1677ff
            }
          },
        }
      ],
      grid: {
        top: '5%',    // 调整顶部留白
        bottom: '10%', // 调整底部留白
        left: '3%',
        right: '5%',
        containLabel: true
      },
      dataZoom: props.config.hasZoom ? [
        {
          show: true,
          realtime: true,
          start: zoomRange.value[0],
          end: zoomRange.value[1],
          // xAxisIndex: [0, 1]
          bottom: '0px'
        },
        {
          type: 'inside',
        }
      ] : null
    }
  })
})

// const beforeUpload = (file) => {
//   console.log('ff:', file, file.status)
//   const reader = new FileReader();
//     reader.onload = (e) => {
//       const content = e.target.result;
//       console.log('read file:', content); // 这里可以处理txt文件内容
//     };
//     reader.readAsText(file.originFileObj);
//   return true

// }
// const uploadChange = (info) => {
//   const { file } = info;
//   // if (file.status === 'done') {
//     const reader = new FileReader();
//     reader.onload = (e) => {
//       const content = e.target.result;
//       // console.log('read file:', content); // 这里可以处理txt文件内容
//     };
//     reader.readAsText(file.originFileObj);
//   // }
// }

const customRequest = (options) => {
  const reader = new FileReader();
  uploadData.value = []
  selfData.value = copySelfData
  reader.onload = (e) => {
    const content = e.target.result;
    content.split(/\r*\n+/).forEach(item => {
      if (item) {
        uploadData.value.push(parseFloat(item))
      }
    })
    console.log('file content:', uploadData.value);
    
    selfData.value = {
      ...selfData.value,
      series: [{
        ...selfData.value.series[0],
        data: uploadData.value
      }]
    }
    if (curSel.value.id == -10) {
      curSel.value.series[0].data = uploadData.value
    }

    // 模拟上传完成
    options.onSuccess();
  };
  reader.readAsText(options.file);
}

</script>

<style scoped lang="less">
@import '@/style/base.less';

.power_list {
  display: flex;
  flex-wrap: wrap;
  .p_item {
    width: 23.5%;
    border: 2px solid #eee;
    padding: 15px 0;
    margin: 0 1% 0.5% 0;
    &:hover {
      cursor: pointer;
      border-color: @baseColor;
    }
    .chart_wrap {
      height: 200px;
    }
    .title {
      display: flex;
      justify-content: center;
      margin: 5px 0 5px 0;
    }
  }
  .p_item_sel {
    border-color: @baseColor;
  }
}
</style>