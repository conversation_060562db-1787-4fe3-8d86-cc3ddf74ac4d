export default {
  // Page title
  title: 'Home',
  
  // Function guide title
  guideTitle: 'Function Guide',
  
  // Recent calculation projects
  recentProject: {
    title: 'Recent Calculation Projects',
    customerName: 'Customer Name:',
    projectBackground: 'Project Background:',
    viewDetail: 'View Details>'
  },
  
  // My projects
  myProject: {
    title: 'My Projects',
    columns: {
      solutionName: 'Solution Name',
      customerName: 'Customer Name'
    },
    actions: {
      detail: 'Detail',
      pauseStart: 'Pause/Start',
      delete: 'Delete',
      config: 'Config'
    },
    viewDetail: 'View Details>'
  },
  
  // Project statistics
  projectStats: {
    title: 'Project Statistics'
  },
  
  // Empty state
  empty: {
    noData: 'No Data',
    createNewProject: 'Create New Project'
  },
  
  // Capacity calculation steps
  capacityCalculation: {
    title: 'Capacity Calculation',
    steps: {
      step1: {
        title: 'Create New Calculation Project',
        description: 'On the project calculation page, you can create new projects or create new solutions based on existing projects through the new project operation'
      },
      step2: {
        title: 'Edit Project Parameters',
        description: 'On the creation page, enter project information, select solving algorithms and scenarios, and configure related parameters'
      },
      step3: {
        title: 'Get Calculation Solution',
        description: 'After calculation is completed, the solution result page displays information such as green electricity, grid, hydrogen production, and hydrogen storage capacity, as well as power curve information, and supports modifying configuration results for recalculation'
      }
    }
  }
}
