<template>
  <div>
  </div>
</template>

<script setup>
// import { ref } from 'vue'
import { message } from 'ant-design-vue'
import * as XLSX from 'xlsx'

// 接收父组件传递的数据
const props = defineProps({
  detailData: {
    type: Object,
    default: () => ({})
  },
  solutionParams: {
    type: Object,
    default: () => ({})
  },
  chartData: {
    type: Object,
    default: () => ({})
  }
})

// 导出功率分配曲线数据到Excel
const exportPowerAllocationData = () => {
  if (!props.chartData || !props.solutionParams) {
    message.warning('暂无数据可导出')
    return
  }

  try {
    const workbook = XLSX.utils.book_new()
    const topology = props.solutionParams.topology || []

    // 拓扑结构映射：[光伏,风电,电网,储能,制氢,储氢]
    const moduleMapping = {
      pv: { index: 0, name: '光伏' },
      wind: { index: 1, name: '风电' },
      grid: { index: 2, name: '电网' },
      storage: { index: 3, name: '储能' },
      hydrogen: { index: 4, name: '制氢' },
      hydrogenStorage: { index: 5, name: '储氢' }
    }

    // 检查模块是否存在的函数
    const hasModule = (moduleKey) => {
      const moduleInfo = moduleMapping[moduleKey]
      return moduleInfo && topology[moduleInfo.index] === 1
    }

    // 时间序列（假设是8760小时数据）
    const generateTimeLabels = (dataLength) => {
      const labels = []
      for (let i = 0; i < dataLength; i++) {
        const day = Math.floor(i / 24) + 1
        const hour = i % 24
        labels.push(`第${day}天 ${hour.toString().padStart(2, '0')}:00`)
      }
      return labels
    }

    // 导出风光数据
    if (hasModule('pv') || hasModule('wind')) {
      const windLightData = []
      const headers = ['时间']

      // 添加表头
      if (hasModule('pv') && props.chartData.pvData) {
        headers.push('光伏功率(MW)')
      }
      if (hasModule('wind') && props.chartData.windData) {
        headers.push('风电功率(MW)')
      }

      windLightData.push(headers)

      // 获取数据长度
      const dataLength = Math.max(
        props.chartData.pvData?.length || 0,
        props.chartData.windData?.length || 0
      )

      if (dataLength > 0) {
        const timeLabels = generateTimeLabels(dataLength)

        for (let i = 0; i < dataLength; i++) {
          const row = [timeLabels[i]]

          if (hasModule('pv') && props.chartData.pvData) {
            row.push((props.chartData.pvData[i] || 0)) 
          }
          if (hasModule('wind') && props.chartData.windData) {
            row.push((props.chartData.windData[i] || 0)) 
          }

          windLightData.push(row)
        }

        const worksheet = XLSX.utils.aoa_to_sheet(windLightData)
        XLSX.utils.book_append_sheet(workbook, worksheet, '风光')
      }
    }

    // 导出风光一体数据
    if (props.chartData.combinedData && (hasModule('pv') || hasModule('wind'))) {
      const combinedData = []
      combinedData.push(['时间', '风光一体功率(MW)'])

      const timeLabels = generateTimeLabels(props.chartData.combinedData.length)

      for (let i = 0; i < props.chartData.combinedData.length; i++) {
        combinedData.push([
          timeLabels[i],
          (props.chartData.combinedData[i] || 0) / 1000 // 转换为MW
        ])
      }

      const worksheet = XLSX.utils.aoa_to_sheet(combinedData)
      XLSX.utils.book_append_sheet(workbook, worksheet, '风光一体')
    }

    // 导出储能数据
    if (hasModule('storage') && props.chartData.storageData) {
      const storageData = []
      storageData.push(['时间', '储能功率(MW)'])

      const timeLabels = generateTimeLabels(props.chartData.storageData.length)

      for (let i = 0; i < props.chartData.storageData.length; i++) {
        storageData.push([
          timeLabels[i],
          (props.chartData.storageData[i] || 0) / 1000 // 转换为MW
        ])
      }

      const worksheet = XLSX.utils.aoa_to_sheet(storageData)
      XLSX.utils.book_append_sheet(workbook, worksheet, '储能')
    }

    // 导出电网数据
    if (hasModule('grid') && props.chartData.gridData) {
      const gridData = []
      gridData.push(['时间', '电网功率(MW)'])

      const timeLabels = generateTimeLabels(props.chartData.gridData.length)

      for (let i = 0; i < props.chartData.gridData.length; i++) {
        gridData.push([
          timeLabels[i],
          (props.chartData.gridData[i] || 0) / 1000 // 转换为MW
        ])
      }

      const worksheet = XLSX.utils.aoa_to_sheet(gridData)
      XLSX.utils.book_append_sheet(workbook, worksheet, '电网')
    }

    // 导出储能SOC数据
    if (hasModule('storage') && props.chartData.socData) {
      const socData = []
      socData.push(['时间', '储能SOC(%)'])

      const timeLabels = generateTimeLabels(props.chartData.socData.length)

      for (let i = 0; i < props.chartData.socData.length; i++) {
        socData.push([
          timeLabels[i],
          props.chartData.socData[i] || 0
        ])
      }

      const worksheet = XLSX.utils.aoa_to_sheet(socData)
      XLSX.utils.book_append_sheet(workbook, worksheet, '储能SOC')
    }

    // 导出电解槽总功率数据
    if (hasModule('hydrogen') && props.chartData.electrolyzerPower) {
      const electrolyzerData = []
      electrolyzerData.push(['时间', '电解槽总功率(MW)'])

      const timeLabels = generateTimeLabels(props.chartData.electrolyzerPower.length)

      for (let i = 0; i < props.chartData.electrolyzerPower.length; i++) {
        electrolyzerData.push([
          timeLabels[i],
          (props.chartData.electrolyzerPower[i] || 0) / 1000 // 转换为MW
        ])
      }

      const worksheet = XLSX.utils.aoa_to_sheet(electrolyzerData)
      XLSX.utils.book_append_sheet(workbook, worksheet, '电解槽总')
    }

    

    // 导出制氢运行曲线数据
    if (hasModule('hydrogen') && props.chartData.produce_h2) {
      const hydrogenProductionData = []
      const headers = ['时间']

      // 添加产氢量列
      if (props.chartData.produce_h2) {
        headers.push('产氢量(Nm³)')
      }

      // 如果有储氢模块，添加供氢量列
      if (hasModule('hydrogenStorage') && props.chartData.supply_h2) {
        headers.push('供氢量(Nm³)')
      }

      hydrogenProductionData.push(headers)

      const dataLength = props.chartData.produce_h2.length
      const timeLabels = generateTimeLabels(dataLength)

      for (let i = 0; i < dataLength; i++) {
        const row = [timeLabels[i]]

        // 添加产氢量数据
        if (props.chartData.produce_h2) {
          row.push(props.chartData.produce_h2[i] || 0)
        }

        // 添加供氢量数据（如果有储氢模块）
        if (hasModule('hydrogenStorage') && props.chartData.supply_h2) {
          row.push(props.chartData.supply_h2[i] || 0)
        }

        hydrogenProductionData.push(row)
      }

      const worksheet = XLSX.utils.aoa_to_sheet(hydrogenProductionData)
      XLSX.utils.book_append_sheet(workbook, worksheet, '制氢运行曲线')
    }

    // 导出储氢曲线数据
    if (hasModule('hydrogenStorage') && props.chartData.store_h2) {
      const hydrogenStorageData = []
      hydrogenStorageData.push(['时间', '储氢量(%)'])

      const timeLabels = generateTimeLabels(props.chartData.store_h2.length)

      for (let i = 0; i < props.chartData.store_h2.length; i++) {
        hydrogenStorageData.push([
          timeLabels[i],
          ((props.chartData.store_h2[i] || 0) * 100).toFixed(2)
        ])
      }

      const worksheet = XLSX.utils.aoa_to_sheet(hydrogenStorageData)
      XLSX.utils.book_append_sheet(workbook, worksheet, '储氢曲线')
    }

    // 导出弃电量曲线数据
    if (props.chartData.abort_ge) {
      const abandonedPowerData = []
      abandonedPowerData.push(['时间', '弃电量(MWh)'])

      const timeLabels = generateTimeLabels(props.chartData.abort_ge.length)

      for (let i = 0; i < props.chartData.abort_ge.length; i++) {
        abandonedPowerData.push([
          timeLabels[i],
          (props.chartData.abort_ge[i] || 0) / 1000 // 转换为MWh
        ])
      }

      const worksheet = XLSX.utils.aoa_to_sheet(abandonedPowerData)
      XLSX.utils.book_append_sheet(workbook, worksheet, '弃电量曲线')
    }

    // 导出日运行小时数曲线数据
    if (props.chartData.run_hours) {
      const runHoursData = []
      runHoursData.push(['日期', '日运行小时数(h)'])

      // 日运行小时数通常是365天的数据
      for (let i = 0; i < props.chartData.run_hours.length; i++) {
        runHoursData.push([
          `第${i + 1}天`,
          props.chartData.run_hours[i] || 0
        ])
      }

      const worksheet = XLSX.utils.aoa_to_sheet(runHoursData)
      XLSX.utils.book_append_sheet(workbook, worksheet, '日运行小时数曲线')
    }

    // 导出各个电解槽数据
    if (hasModule('hydrogen') && props.chartData.ele_all_output) {
      const electrolyzerOutputs = props.chartData.ele_all_output

      if (electrolyzerOutputs && Array.isArray(electrolyzerOutputs)) {
        electrolyzerOutputs.forEach((electrolyzerData, index) => {
          if (electrolyzerData && electrolyzerData.data) {
            const sheetData = []
            sheetData.push(['时间', `电解槽${index + 1}功率(MW)`])

            const timeLabels = generateTimeLabels(electrolyzerData.data.length)

            for (let i = 0; i < electrolyzerData.data.length; i++) {
              sheetData.push([
                timeLabels[i],
                (electrolyzerData.data[i] || 0) / 1000 // 转换为MW
              ])
            }

            const worksheet = XLSX.utils.aoa_to_sheet(sheetData)
            XLSX.utils.book_append_sheet(workbook, worksheet, `电解槽${index + 1}`)
          }
        })
      }
    }

    // 如果没有添加任何工作表，则不导出
    if (workbook.SheetNames.length === 0) {
      message.warning('当前拓扑结构下暂无可导出的数据')
      return
    }

    // 导出文件
    const fileName = `功率分配曲线数据_${props.solutionParams.projectName || '未命名项目'}_${new Date().toISOString().slice(0, 10)}.xlsx`
    XLSX.writeFile(workbook, fileName)

    message.success('数据导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  }
}

// 暴露导出方法给父组件
defineExpose({
  exportPowerAllocationData
})
</script>

<style scoped>
/* 这个组件不需要样式 */
</style>