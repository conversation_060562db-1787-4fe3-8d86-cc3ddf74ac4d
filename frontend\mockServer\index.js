import express from 'express'
import { initProject } from './project'

// import { createProxyMiddleware } from 'http-proxy-middleware'
import bodyParser from 'body-parser'

const app = express()
app.use(bodyParser())

app.use((_, res, next) => {
  res.header('Access-Control-Allow-Origin', '*')
  res.header('Access-Control-Allow-Headers', 'Authorization,X-API-KEY, Origin, X-Requested-With, Content-Type, Accept, Access-Control-Request-Method' )
  res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PATCH, PUT, DELETE')
  res.header('Allow', 'GET, POST, PATCH, OPTIONS, PUT, DELETE')
  next();
});
  
initProject(app)

// 自定义代理规则
// app.use(createProxyMiddleware('/api/v1', {
//   target: 'http://************:8889', // target host
//   changeOrigin: true, // needed for virtual hosted sites
//   ws: true, // proxy websockets
//   pathRewrite: {
//     '^/api/v1': '/api/v1', // rewrite path
//   }
// }));


export function expressPlugin() {
  return {
    name: 'express-plugin',
    configureServer(server) {
      server.middlewares.use(app)
    }
  }
}
