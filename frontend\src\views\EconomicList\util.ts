// 定义数据类型接口
interface SolutionData {
  id: string
  solutionName: string
  scenarioType: string
  profitRate: number
  submitTime: string
  status: string
}

interface ProjectData {
  id: string
  projectId: number
  projectName: string
  projectBackground: string
  customerName: string
  solutions: SolutionData[]
}

// 主表格列定义（项目级别）
export const columns = [
  // {
  //   title: '序号',
  //   key: 'sequence',
  //   dataIndex: 'sequence',
  //   width: 60,
  //   align: 'center' as const
  // },
  {
    title: '项目名称',
    dataIndex: 'name',
    key: 'name',
    width: 100
  },
  {
    title: '项目背景',
    dataIndex: 'desc',
    key: 'desc',
    width: 100,
    ellipsis: true
  },
  {
    title: '客户名称',
    dataIndex: 'customer',
    key: 'customer',
    width: 100
  },
  {
    title: '方案',
    key: 'solutions',
    align: 'center'
  }
]

// 子表格列定义（方案级别）
export const solutionColumns = [
  {
    title: '方案名称',
    dataIndex: 'name',
    key: 'name',
    width: 150
  },
  {
    title: '方案描述',
    dataIndex: 'desc',
    key: 'desc',
    width: 150,
    // align: 'center'
  },
  {
    title: '场景类型',
    dataIndex: 'topology',
    key: 'topology',
    width: 230,
    align: 'center'
  },
  {
    title: '项目收益率',
    dataIndex: 'metrics',
    key: 'metrics',
    width: 130,
    // align: 'center'
  },
   {
    title: 'LCOH(元/Nm³)',
    dataIndex: 'lcoh',
    key: 'lcoh',
    width: 90,
    // align: 'center'
  },
  {
    title: '提交时间',
    // dataIndex: 'createTime',
    key: 'createTime',
    width: 160
  },
  // {
  //   title: '状态',
  //   dataIndex: 'status',
  //   key: 'status',
  //   width: 80,
  //   align: 'center' as const
  // },
  {
    title: '操作',
    key: 'action',
    width: 100,
    // align: 'center' as const
  }
]

// 状态配置
export const statusConfig = {
  processing: { label: '计算中', color: 'processing' },
  success: { label: '成功', color: 'success' },
  failed: { label: '失败', color: 'error' }
}

// 获取状态颜色
export const getStatusColor = (status: string) => {
  return statusConfig[status as keyof typeof statusConfig]?.color || 'default'
}

// 获取状态文本
export const getStatusText = (status: string) => {
  return statusConfig[status as keyof typeof statusConfig]?.label || status
}

// 导出工具函数
export const exportTableData = (selectedData: SolutionData[]) => {
  console.log('导出数据:', selectedData)
  // 这里可以实现实际的导出逻辑
}

// 方案对比函数
export const compareSolutions = (solutions: SolutionData[]) => {
  console.log('对比方案:', solutions)
  // 这里可以实现实际的对比逻辑
}

// 场景类型定义 - 与ProjectList保持一致
export const sceneTypes = () => {
  return [
    '光伏',
    '风机', 
    '电网',
    '储能',
    '制氢',
    '储氢',
  ]
}

// 创建场景标签列表
export const createSceneTagList = (projectScene: number[] | undefined) => {
  const tResult: { label: string }[] = []
  const sceneList = sceneTypes()
  
  // 如果projectScene不存在或不是数组，返回空数组
  if (!projectScene || !Array.isArray(projectScene)) {
    return tResult
  }
  
  for (let i = 0, len = sceneList.length; i < len; i++) {
    if (projectScene[i] === 1) {
      tResult.push({
        label: sceneList[i],
      })
    }
  }
  return tResult
}
