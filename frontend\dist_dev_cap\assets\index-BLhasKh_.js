import{_ as Te,r as v,D as Pe,v as Se,d as b,b as o,l as f,w as r,e as a,c as k,i as I,F as q,k as D,f as c,j as R,E as A,t as z,u as aa,s as ta,x as la,o as sa,A as de,h as C,G as na,B,C as O,H as oa,p as ia,m as ua}from"./index-Blktg-U-.js";import{L as De,_ as ra,c as da,a as ca,b as _a,d as va,e as pa}from"./index-Bcl2oRw4.js";import{d as ma,e as fa,f as Re,h as he,i as ya,j as ga}from"./index-BbGSYVSM.js";import{g as Le}from"./index-CB8EjQs8.js";import{l as ba}from"./lodash-DsxhgM2J.js";/* empty css                                                              */const ha=G=>[{label:"项目名称",name:"projectName",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"string"},{label:"客户名称",name:"customer",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"string"},{label:"项目周期",name:"cycle",unit:"年",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"年产氢量",name:"h2Product",unit:"kg",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"贷款利率",name:"loanRate",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio"},{label:"贷款周期",name:"loanCycle",unit:"年",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"贷款比例",name:"loanRadio",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio"},{label:"项目描述",name:"desc",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"textarea"}],wa=()=>[{label:"LCOH",name:"target0",unit:"",default:1,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"投资成本最低",name:"target1",unit:"",default:0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"弃电率最低",name:"target2",unit:"",default:0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"产氢量最大",name:"target3",unit:"",default:0,rules:[{required:!1,message:"请输入"}],type:"number"}],ka=()=>[{label:"快速测算",name:"",default:1}],xa=()=>[{label:"用水价格",name:"water_price",unit:"元/吨",default:void 0,rules:[{required:!0,message:"请输入"}],type:"select",options:[{label:"4.38元/吨",value:4.38}]},{label:"制氢耗水量",name:"h2_water_consuming",unit:"L/Nm³",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"制氢策略",name:"ele_policy",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"select",options:[{label:"满功率",value:1}]}],Ca=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:""},{title:"类型",dataIndex:"type",key:"type",s_type:"select",rules:[{required:!1}],unit:"",options:[{label:"ALK",value:1,color:"blue"},{label:"PEM",value:2,color:"green"}]},{title:"容量",dataIndex:"capacity"},{title:"价格",dataIndex:"price"},{title:"电耗(kWh/Nm³)",dataIndex:"power_consumption"},{title:"额定功率(kw)",dataIndex:"pe"},{title:"最低负载率",dataIndex:"lower_load_rate"},{title:"最高负载率",dataIndex:"upper_load_rate"},{title:"年衰减曲线",dataIndex:"damp_curve"},{title:"辅助系统能耗(kWh/Nm³)",dataIndex:"assist_consumption"}],Ia=()=>[{label:"年下电比例",name:"grid_down_radio",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",max:1,numberType:"ratio"},{label:"绿电最大上网比例",name:"grid_up_radio",unit:"",default:.2,rules:[{required:!1,message:"请输入"}],type:"number",max:1,numberType:"ratio"},{label:"绿电上网价格",name:"grid_sale_price",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"select",options:[{label:"0.332元/kwh",value:.332}]}],qa=()=>[{label:"控制策略",name:"es_policy",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"select",options:[{label:"削峰填谷",value:1}]}],za=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:""},{title:"单机功率(MW)",dataIndex:"single_power"},{title:"充电效率",dataIndex:"charge_efficiency"},{title:"放电效率",dataIndex:"discharge_efficiency"},{title:"充放电倍率",dataIndex:"c_rate"},{title:"储能占比",dataIndex:"radio"},{title:"初始SOC",dataIndex:"init_soc"},{title:"SOC下限",dataIndex:"min_soc"},{title:"SOC上限",dataIndex:"max_soc"},{title:"置信度",dataIndex:"confidence"},{title:"寿命(年)",dataIndex:"life_cycle"}],Ua=()=>[{label:"储罐容量下限(kg)",name:"hs_min_capacity",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"储罐容量上限(kg)",name:"hs_max_capacity",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"最大升负荷速率",name:"max_increase_load_rate",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"最大降负荷速率",name:"max_down_load_rate",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"负荷调整时间",name:"adjust_time",unit:"min",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"负荷调节间隔",name:"adjust_interval",unit:"min",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"}],Sa=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:""},{title:"体积(m³)",dataIndex:"volume"},{title:"最低运行压力",dataIndex:"min_pressure"},{title:"最大运行压力",dataIndex:"max_pressure"},{title:"价格",dataIndex:"price"},{title:"占地面积(㎡)",dataIndex:"area"}],Va=()=>[{label:"光伏EPC投资",name:"pv_epc",unit:"元/W",default:2.9,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"风电EPC投资",name:"wind_epc",unit:"元/W",default:4.2,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"储能EPC投资",name:"es_epc",unit:"元/W",default:1.3,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"制氢系统投资",name:"h2_invest",unit:"元/W",default:2,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"制氢厂房投资",name:"plant_invest",unit:"元/W",default:1.5,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"储氢系统投资",name:"hs_invest",unit:"元/kg",default:2,rules:[{required:!0,message:"请输入"}],type:"number"}],$a=()=>[{label:"设备折现率",name:"discount_rate",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number",numberType:"ratio"},{label:"负荷缺失率",name:"负荷缺失率",unit:"",default:0,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio"},{label:"光伏运维比例",name:"pv_om_radio",unit:"",default:.25,rules:[{required:!0,message:"请输入"}],type:"number",numberType:"ratio"},{label:"光伏运营成本",name:"pv_om_cost",unit:"元/W/年",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"风电运维比例",name:"wind_om_radio",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number",numberType:"ratio"},{label:"风电运营成本",name:"wind_om_cost",unit:"元/W/年",default:.3,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"储能运维比例",name:"es_om_radio",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number",numberType:"ratio"},{label:"储能运营成本",name:"es_om_cost",unit:"元/W/年",default:.0675,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"制氢运维比例",name:"h2_om_radio",unit:"",default:.02,rules:[{required:!0,message:"请输入"}],type:"number",numberType:"ratio"},{label:"制氢运营成本",name:"h2_om_cost",unit:"元/W/年",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"储氢运维比例",name:"hs_om_radio",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number",numberType:"ratio"},{label:"储氢运维成本",name:"hs_om_cost",unit:"元/kg/年",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number"}],Da={projectName:void 0,customer:void 0,desc:void 0,h2Product:void 0,cycle:void 0,loanRadio:void 0,loanCycle:void 0,loanRate:void 0,solutionName:void 0,topology:[1,0,1,1,1,0],targetExpr:[1,0,0,0],algorithm:1,pv_min_capacity:0,pv_max_capacity:void 0,pv_dev_ids:void 0,pv_damp_rate:void 0,wind_min_capacity:0,wind_max_capacity:void 0,grid_down_radio:.1,grid_zone_id:void 0,grid_year:void 0,grid_sale_price:.332,grid_up_radio:.2,es_dev_ids:[],es_min_capacity:0,es_max_capacity:void 0,es_policy:1,ele_dev_ids:[],ele_min_capacity:0,ele_max_capacity:void 0,ele_policy:1,water_price:4.38,h2_water_consuming:1.4,hs_dev_ids:[],hs_min_capacity:0,hs_max_capacity:void 0,max_increase_load_rate:void 0,max_down_load_rate:void 0,adjust_time:void 0,adjust_interval:void 0,absorb_id:2,pv_forecast_zone_id:void 0,pv_forecast:void 0,wind_forecast_zone_id:void 0,wind_forecast:void 0,pv_epc:2.9,wind_epc:4.2,es_epc:.8,h2_invest:2,plant_invest:1.5,hs_invest:39200,discount_rate:.06,pv_om_radio:.02,pv_om_cost:.25,wind_om_radio:.02,wind_om_cost:.3,es_om_radio:.02,es_om_cost:.0675,h2_om_radio:.02,h2_om_cost:7,hs_om_radio:.02,hs_om_cost:1e3},Ra={projectName:"项目t1",customer:"kiwi",desc:void 0,h2Product:void 0,cycle:void 0,loanRadio:void 0,loanCycle:void 0,loanRate:void 0,solutionName:void 0,topology:[1,0,1,1,1,0],targetExpr:[1,0,0,0],algorithm:1,pv_min_capacity:0,pv_max_capacity:void 0,pv_dev_ids:void 0,pv_damp_rate:void 0,wind_min_capacity:0,wind_max_capacity:void 0,grid_down_radio:.1,grid_zone_id:void 0,grid_year:void 0,grid_sale_price:.332,grid_up_radio:.2,es_dev_ids:[],es_min_capacity:0,es_max_capacity:void 0,es_policy:1,ele_dev_ids:[],ele_min_capacity:0,ele_max_capacity:void 0,ele_policy:1,water_price:10.1,h2_water_consuming:30,hs_dev_ids:[],hs_min_capacity:0,hs_max_capacity:void 0,max_increase_load_rate:void 0,max_down_load_rate:void 0,adjust_time:void 0,adjust_interval:void 0,absorb_id:2,pv_forecast_zone_id:void 0,pv_forecast:void 0,wind_forecast_zone_id:void 0,wind_forecast:void 0,pv_epc:2.9,wind_epc:4.2,es_epc:.8,h2_invest:2,plant_invest:1.5,hs_invest:39200,discount_rate:.06,pv_om_radio:.02,pv_om_cost:.25,wind_om_radio:.02,wind_om_cost:.3,es_om_radio:.02,es_om_cost:.0675,h2_om_radio:.02,h2_om_cost:7,hs_om_radio:.02,hs_om_cost:1e3},La={class:"power_list"},Ta=["onClick"],Pa={class:"title"},Ka={class:"title"},ja={__name:"index",props:["selfData","chartList","config"],emits:["submit"],setup(G,{expose:ce,emit:Z}){const K=G,t=v([40,50]),h=v({pvPower:[]});v(1);const U=v({});v([]);const E=v([]),j=v({desc:"自定义地区",id:-10,title:"",series:[{data:K.selfData,type:"line",lineStyle:{normal:{color:"#1677ff"}}}],grid:{top:"5%",bottom:"10%",left:"3%",right:"5%",containLabel:!0},dataZoom:K.config.hasZoom?[{show:!0,realtime:!0,start:t.value[0],end:t.value[1],bottom:"0px"},{type:"inside"}]:null}),ke=ba.cloneDeep(j.value),F=v(!1),se=Z,M=L=>{U.value=L};ce({open:()=>{F.value=!0},close:()=>{F.value=!1}});const ve=()=>{se("submit",U.value)};Pe(()=>K.chartList,L=>{});const ne=Se(()=>{var L;return U.value=K.config.default||{},(L=K.chartList)==null?void 0:L.map(T=>{const{yData:H,unit:J,desc:P,id:Q}=T;return{desc:P,id:Q,title:"",series:[{data:H,type:"line",lineStyle:{normal:{color:"#1677ff"}}}],grid:{top:"5%",bottom:"10%",left:"3%",right:"5%",containLabel:!0},dataZoom:K.config.hasZoom?[{show:!0,realtime:!0,start:t.value[0],end:t.value[1],bottom:"0px"},{type:"inside"}]:null}})}),Ce=L=>{const T=new FileReader;E.value=[],j.value=ke,T.onload=H=>{H.target.result.split(/\r*\n+/).forEach(P=>{P&&E.value.push(parseFloat(P))}),console.log("file content:",E.value),j.value={...j.value,series:[{...j.value.series[0],data:E.value}]},U.value.id==-10&&(U.value.series[0].data=E.value),L.onSuccess()},T.readAsText(L.file)};return(L,T)=>{const H=b("UploadOutlined"),J=b("a-button"),P=b("a-upload"),Q=b("a-modal");return o(),f(Q,{width:"90%",open:F.value,"onUpdate:open":T[2]||(T[2]=ae=>F.value=ae),title:K.config.modalTitle,onOk:ve},{default:r(()=>{var ae;return[a("div",La,[(ae=ne.value)!=null&&ae.length?(o(!0),k(q,{key:0},I(ne.value,W=>(o(),k("div",{class:A(["p_item",{p_item_sel:W.id===U.value.id}]),onClick:Ie=>M(W)},[c(De,{data:W,class:"chart_wrap"},null,8,["data"]),a("div",Pa,z(W.desc),1)],10,Ta))),256)):D("",!0),K.config.canUploadSelf?(o(),k("div",{key:1,class:A(["p_item",{p_item_sel:U.value.id===-10}]),onClick:T[1]||(T[1]=W=>M(j.value))},[c(De,{data:j.value,class:"chart_wrap"},null,8,["data"]),a("div",Ka,[c(P,{onChange2:L.uploadChange,"before-upload2":W=>L.beforeUpload(W),customRequest:Ce,onRemove:T[0]||(T[0]=()=>{h.value.pvPower=[]}),accept:".txt",maxCount:1},{default:r(()=>[c(J,{size:"small",style:{fontSize:"11px",marginRight:"10px"}},{icon:r(()=>[c(H)]),default:r(()=>[R(" 上传数据 ")]),_:1})]),_:1},8,["onChange2","before-upload2"]),R(" 自定义数据 ")])],2)):D("",!0)])]}),_:1},8,["open","title"])}}},we=Te(ja,[["__scopeId","data-v-e42955b1"]]),y=G=>(ia("data-v-ab12b5e2"),G=G(),ua(),G),Ea={class:"body_wrap"},Wa={class:"part_wrap"},Aa={class:"content_wrap"},Fa={class:"box_wrap"},Ma=y(()=>a("div",{class:"b_title"},"项目信息",-1)),Na={class:"b_body"},Ba={class:"line_item"},Oa={class:"box_wrap"},Ga=y(()=>a("div",{class:"b_title"},"求解方式",-1)),Za={class:"b_body"},Ha={class:"goal_list"},Ja=y(()=>a("div",{class:"g_title"},"求解目标选择",-1)),Qa=["onClick","name","rules"],Xa={class:"goal_item"},Ya={class:"name"},et={class:"v_line"},at={class:"number_input"},tt=y(()=>a("div",{class:"g_tips"},"选择多个求解目标后，需设置每个目标的权重，总权重值需为100%",-1)),lt={class:"goal_list"},st=y(()=>a("div",{class:"g_title"},"求解算法",-1)),nt={class:"box_wrap"},ot=y(()=>a("div",{class:"b_title"},"场景选择",-1)),it={class:"b_body"},ut={class:"scene_title_wrap"},rt={class:"pv_wind_wrap"},dt=y(()=>a("div",{class:"left"},[a("img",{src:ra})],-1)),ct=y(()=>a("div",{class:"right"},[a("div",{class:"r_title"},"光伏"),a("div",{class:"r_desc"},"选择光伏场景后可配置光伏参数")],-1)),_t=[dt,ct],vt=y(()=>a("div",{class:"left"},[a("img",{src:da})],-1)),pt=y(()=>a("div",{class:"right"},[a("div",{class:"r_title"},"风机"),a("div",{class:"r_desc"},"选择风机场景后可配置风机参数")],-1)),mt=[vt,pt],ft=y(()=>a("div",{class:"left"},[a("img",{src:ca})],-1)),yt=y(()=>a("div",{class:"right"},[a("div",{class:"r_title"},"电网"),a("div",{class:"r_desc"},"选择电网场景后可配置电网用电")],-1)),gt=[ft,yt],bt=y(()=>a("div",{class:"left"},[a("img",{src:_a})],-1)),ht=y(()=>a("div",{class:"right"},[a("div",{class:"r_title"},"储能"),a("div",{class:"r_desc"},"选择储能场景后可配置储能参数")],-1)),wt=[bt,ht],kt=y(()=>a("div",{class:"left"},[a("img",{src:va})],-1)),xt=y(()=>a("div",{class:"right"},[a("div",{class:"r_title"},"制氢"),a("div",{class:"r_desc"},"选择制氢场景后可配置制氢参数")],-1)),Ct=[kt,xt],It=y(()=>a("div",{class:"left"},[a("img",{src:pa})],-1)),qt=y(()=>a("div",{class:"right"},[a("div",{class:"r_title"},"储氢"),a("div",{class:"r_desc"},"选择储氢场景后可配置储氢参数")],-1)),zt=[It,qt],Ut={class:"scene_content_wrap"},St={class:"k_v_list"},Vt={class:"k_v_item"},$t=y(()=>a("div",{class:"label"},"出力曲线:",-1)),Dt={class:"value"},Rt={class:"desc_wrap"},Lt={class:"k_v_item"},Tt=y(()=>a("div",{class:"label"},"光伏年衰减率:",-1)),Pt={class:"value"},Kt={class:"desc_wrap"},jt={class:"k_v_item"},Et=y(()=>a("div",{class:"label"},"容量范围(MW):",-1)),Wt={class:"value range_item"},At=y(()=>a("div",{class:"middle_line"},"—",-1)),Ft={class:"k_v_list"},Mt={class:"k_v_item"},Nt=y(()=>a("div",{class:"label"},"出力曲线:",-1)),Bt={class:"value"},Ot={class:"desc_wrap"},Gt={class:"k_v_item"},Zt=y(()=>a("div",{class:"label"},"容量范围(MW):",-1)),Ht={class:"value range_item"},Jt=y(()=>a("div",{class:"middle_line"},"—",-1)),Qt={class:"line_item"},Xt={class:"grid_price"},Yt=y(()=>a("a",{href:"#",style:{width:"42px",display:"block","margin-right":"5px"}},"请选择",-1)),el={key:0},al={class:"line_item"},tl={class:"range_item"},ll=y(()=>a("div",{class:"middle_line"},"—",-1)),sl={class:"common_form_item"},nl=y(()=>a("div",{class:"i_label"},"储能电池选择",-1)),ol={class:"line_item"},il={class:"range_item"},ul=y(()=>a("div",{class:"middle_line"},"—",-1)),rl={class:"common_form_item"},dl=y(()=>a("div",{class:"i_label"},"电解槽选择",-1)),cl={class:"line_item"},_l={class:"k_v_list"},vl={class:"k_v_item",style:{margin:"0 0 10px 0"}},pl=y(()=>a("div",{class:"label"},"供氢曲线",-1)),ml={class:"value"},fl={class:"desc_wrap"},yl={class:"common_form_item"},gl=y(()=>a("div",{class:"i_label"},"储氢罐选择",-1)),bl={class:"box_wrap"},hl=y(()=>a("div",{class:"b_title"},"成本配置",-1)),wl={class:"b_body"},kl={class:"line_item"},xl={class:"line_item"},Cl={class:"button_wrap"},Il={__name:"index",setup(G){const ce=aa(),Z=ta(),K=v();v(!1);const t=v({}),h=la({selectedAlkRowKeys:[],selectedBatRowKeys:[],loading:!1,running:!1}),U=v({alk:[],bat:[],alkStorage:[]}),E=v([{title:"光伏",key:0},{title:"风机",key:1},{title:"电网",key:2},{title:"储能",key:3},{title:"制氢",key:4},{title:"储氢",key:5}]),j=v([1,2]),ke=Se(()=>{const{projectId:n,solutionId:l}=Z.query;return n?l?"修改方案":"新建方案":"创建项目"}),F=v(),se=v(),M=v({}),xe=v({}),_e=v({}),ve=v({}),ne=v(),Ce=n=>{var l,d,p;console.log("pv power:",n),F.value.close(),_e.value=n,t.value.pv_forecast_zone_id=n.id,n.id==-10?(t.value.pv_forecast={data:(p=(d=(l=n==null?void 0:n.series)==null?void 0:l[0])==null?void 0:d.data)!=null&&p.length?n.series[0].data:void 0},t.value.pv_forecast_zone_id=-1):t.value.pv_forecast=null},L=n=>{console.log("pv device:",n),se.value.close(),ve.value=n,t.value.pv_dev_ids=[n.id]},T=async()=>{const{code:n,msg:l,data:d}=await Re({type:1});M.value=d.result.map(p=>{const{zone:u,forecast:_}=p,{id:w,region:g,country:m,province:V,city:x}=u;return{yData:_.data,unit:"",desc:[V,x].filter($=>!!$).join("_"),id:w}})},H=async()=>{const{code:n,msg:l,data:d}=await he({type:1});xe.value=d.result.map(p=>{const{baseInfo:{id:u,manufacturer:_,model:w},params:g}=p;return{yData:g.damp_curve,unit:"",desc:[_,w].filter(m=>!!m).join("_"),id:u}}),console.log("pv forecast:",M.value)},J=v();v();const P=v({});v({});const Q=v({});v({});const ae=n=>{var l,d,p;console.log("wind power:",n),J.value.close(),Q.value=n,t.value.wind_forecast_zone_id=n.id,n.id==-10?(t.value.wind_forecast={data:(p=(d=(l=n==null?void 0:n.series)==null?void 0:l[0])==null?void 0:d.data)!=null&&p.length?n.series[0].data:void 0},t.value.wind_forecast_zone_id=-1):t.value.wind_forecast=null},W=async()=>{const{code:n,msg:l,data:d}=await Re({type:2});P.value=d.result.map(p=>{const{zone:u,forecast:_}=p,{id:w,region:g,country:m,province:V,city:x}=u;return{yData:_.data,unit:"",desc:[V,x].filter($=>!!$).join("_"),id:w}}),console.log("wind forecast:",P.value)},Ie=(n,l)=>{l||(X.value[n]=!X.value[n]),t.value.targetExpr=X.value.map((d,p)=>d?t.value[`target${p}`]:0),console.log("targetExpr:",t.value.targetExpr)},qe=v([]),pe=v(4),X=v([!1,!1,!1,!1]),S=v([!1,!1,!1,!1,!1,!1]),te=n=>{S.value[n]=!S.value[n],t.value.topology=S.value.map((l,d)=>l?1:0),qe.value=E.value.filter((l,d)=>S.value[d]),S.value[n]&&(pe.value=n)};Se(()=>h.selectedAlkRowKeys.length>0);const Ke=n=>{console.log("selectedAlkRowKeys changed: ",n),h.selectedAlkRowKeys=n,t.value.ele_dev_ids=n},je=async()=>{const{code:n,msg:l,data:d}=await he({type:4});U.value.alk=d.result.map(p=>{const{baseInfo:u,params:_}=p;return{...u,..._}})},ze=v([]),me=v({}),Ee=async()=>{const{code:n,msg:l,data:d}=await ya();ze.value=d.result.map(p=>{const{price:u,zone:{city:_,country:w,id:g,province:m,region:V}}=p;return{label:[_].filter(x=>!!x).join("_"),value:g,children:Object.keys(u).map(x=>{const $=parseInt(x);return{label:$,value:$}})}}),console.log("grid price:",d.result,ze.value)},We=(n,l)=>{console.log("slect price:",n,l),t.value.grid_zone_id=n[0],t.value.grid_year=n[1],me.value=l},Ae=async()=>{const{code:n,msg:l,data:d}=await he({type:3});U.value.bat=d.result.map(p=>{const{baseInfo:u,params:_}=p;return{...u,..._}})},Fe=n=>{console.log("selectedBatRowKeys changed: ",n),h.selectedBatRowKeys=n,t.value.es_dev_ids=n},Ve=v([]),Ue=v(),$e=v({}),Me=async()=>{const{code:n,msg:l,data:d}=await he({type:5});U.value.alkStorage=d.result.map(p=>{const{baseInfo:u,params:_}=p;return{...u,..._}})},Ne=n=>{console.log("alk storage:",n),Ue.value.close(),$e.value=n,t.value.absorb_id=n.id},Be=n=>{console.log("selectedAlkStoreRowKeys changed: ",n),h.selectedAlkStoreRowKeys=n,t.value.hs_dev_ids=n},Oe=async()=>{const{code:n,msg:l,data:d}=await ga();Ve.value=d.result.map(p=>{const{id:u,name:_,demandCurve:w}=p;return{yData:w.data,unit:"",desc:_,id:u}}),console.log("wind forecast:",P.value)},Ge=()=>{var u,_,w,g;if(t.value.targetExpr.reduce((m,V)=>m+V)!==1)return de.warn("已选求解目标权重总和需为100% !"),!1;const l=t.value.pv_forecast_zone_id>0||((_=(u=t.value.pv_forecast)==null?void 0:u.data)==null?void 0:_.length),d=t.value.wind_forecast_zone_id>0||((g=(w=t.value.wind_forecast)==null?void 0:w.data)==null?void 0:g.length);return l||d?!0:(de.warn("光伏和风机出力曲线请至少选择1项!"),!1)},Ze=async()=>{const n=await K.value.validateFields(),l={...n,...t.value};if(["target0","target1","target2","target3","scene0","scene1","scene2","scene3","scene4","scene5"].forEach(g=>l.hasOwnProperty(g)&&delete l[g]),console.log("re:",l,n,t.value),!Ge())return;h.loading=!0,h.running=!0;const{code:u,msg:_,data:w}=await ma(l);if(h.running=!1,h.loading=!1,u===0){de.success("已提交运行");const g={taskId:w.taskId},{projectId:m,solutionId:V}=Z.query;m&&(g.projectId=m),V&&(g.solutionId=V);const x=ce.resolve({name:"projectDetail",query:g});window.open(x.href,"_blank")}else de.error(_)},He=()=>{t.value=Da},Je=()=>{const{targetExpr:n,topology:l}=t.value,d=["target0","target1","target2","target3"],p=["scene0","scene1","scene2","scene3","scene4","scene5"];d.forEach((u,_)=>{t.value[u]=n[_],X.value[_]=!!n[_]}),p.forEach((u,_)=>{t.value[u]=l[_],S.value[_]=!!l[_]}),qe.value=E.value.filter((u,_)=>S.value[_]),pe.value=E.value.findIndex((u,_)=>S.value[_])};Pe(Z,n=>{console.log("query:",n)});const Qe=async()=>{He();const{projectId:n,solutionId:l}=Z.query;(n||l)&&(h.loading=!0),await T(),await H(),await W(),Ee(),je(),Ae(),Me(),Oe(),l&&(h.loading=!1)},Xe=async()=>{var V,x,$,Y,fe,le,ye,ge,be;const{projectId:n,solutionId:l}=Z.query;if(n==null){console.log("Pure create project");return}const d={};let p={};d.projectId=parseInt(n),l&&(d.solutionId=parseInt(l)),console.log("enter params22:"),h.loading=!0;const{code:u,msg:_,data:{project:w,solution:g,calcParams:m}}=await fa(d);if(h.loading=!1,console.log("enter params:",w,g,m),p={...w,projectName:w.name},n&&l){const{targetExpr:ee,topology:N}=g;p={...p,...g,...m,target0:ee[0],target1:ee[1],target2:ee[2],target3:ee[3],scene0:N[0],scene1:N[1],scene2:N[2],scene3:N[3],scene4:N[4],scene5:N[5]},ne.value=(V=m==null?void 0:m.pv_forecast)==null?void 0:V.data,($=(x=m==null?void 0:m.pv_forecast)==null?void 0:x.data)!=null&&$.length&&M.value.push({yData:(Y=m==null?void 0:m.pv_forecast)==null?void 0:Y.data,desc:`自定义数据-${g.name}`,id:-1}),(le=(fe=m==null?void 0:m.wind_forecast)==null?void 0:fe.data)!=null&&le.length&&P.value.push({yData:(ye=m==null?void 0:m.wind_forecast)==null?void 0:ye.data,desc:`自定义数据-${g.name}`,id:-1}),_e.value={desc:(ge=M.value.find(oe=>oe.id===m.pv_forecast_zone_id))==null?void 0:ge.desc},Q.value={desc:(be=P.value.find(oe=>oe.id===m.wind_forecast_zone_id))==null?void 0:be.desc},h.selectedBatRowKeys=m.es_dev_ids,h.selectedAlkRowKeys=m.ele_dev_ids}n&&!l&&(p={...Ra,...w,projectName:w.name}),u==0?t.value=p:de.error(_)};return sa(async()=>{await Qe(),await Xe(),Je()}),(n,l)=>{const d=b("a-breadcrumb-item"),p=b("a-breadcrumb"),u=b("a-input-number"),_=b("a-input"),w=b("a-textarea"),g=b("a-form-item"),m=b("a-radio-button"),V=b("a-radio-group"),x=b("a-button"),$=b("a-select-option"),Y=b("a-select"),fe=b("a-cascader"),le=b("a-table"),ye=b("a-tag"),ge=b("a-tab-pane"),be=b("a-tabs"),ee=b("a-collapse-panel"),N=b("a-collapse"),oe=b("a-form"),Ye=b("a-spin");return o(),k("div",Ea,[a("div",Wa,[a("div",null,[c(p,null,{default:r(()=>[c(d,null,{default:r(()=>[a("a",{href:"void:0",onClick:l[0]||(l[0]=s=>C(ce).back())}," < 返回")]),_:1}),c(d,null,{default:r(()=>[R(z(ke.value),1)]),_:1})]),_:1})]),a("div",Aa,[c(Ye,{spinning:h.loading},{default:r(()=>[c(oe,{ref_key:"formRef",ref:K,labelAlign:"left",model:t.value,name:"basic","label-col":{span:14},"wrapper-col":{span:12},autocomplete:"off"},{default:r(()=>[a("div",Fa,[Ma,a("div",Na,[a("div",Ba,[(o(!0),k(q,null,I(C(ha)(),s=>(o(),f(g,{class:"line_form_item",label:s.unit?`${s.label}(${s.unit})`:s.label,name:s.name,rules:s.rules},{default:r(()=>[s.type==="number"&&s.numberType==="ratio"?(o(),f(u,{key:0,class:"input_deal_wrap",defaultValue:s.default,value:t.value[s.name],"onUpdate:value":e=>t.value[s.name]=e,size:"small",formatter:e=>`${e*100}%`,parser:e=>parseFloat(e.replace("%",""))/100,min:0,max:1,step:.01},null,8,["defaultValue","value","onUpdate:value","formatter","parser"])):D("",!0),s.type==="number"&&s.numberType!=="ratio"?(o(),f(u,{key:1,style:{width:"80%"},class:"input_deal_wrap",defaultValue:s.default,value:t.value[s.name],"onUpdate:value":e=>t.value[s.name]=e,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):s.type==="string"?(o(),f(_,{key:2,style:{width:"80%"},class:"input_deal_wrap",defaultValue:s.default,value:t.value[s.name],"onUpdate:value":e=>t.value[s.name]=e,size:"small"},null,8,["defaultValue","value","onUpdate:value"])):s.type==="textarea"?(o(),f(w,{key:3,style:na([{width:"80%"},{margin:"5px 0 5px 0"}]),class:"input_deal_wrap",autosize:{maxRows:3},defaultValue:s.default,value:t.value[s.name],"onUpdate:value":e=>t.value[s.name]=e,size:"small"},null,8,["defaultValue","value","onUpdate:value"])):D("",!0)]),_:2},1032,["label","name","rules"]))),256))])])]),a("div",Oa,[Ga,a("div",Za,[a("div",Ha,[Ja,(o(!0),k(q,null,I(C(wa)(),(s,e)=>(o(),k("div",{onClick:i=>Ie(e),name:s.name,rules:s.rules,class:A({sel_item:X.value[e]})},[a("div",Xa,[a("div",Ya,z(s.label),1),B(a("div",et,"|",512),[[O,X.value[e]]]),B(a("div",at,[c(u,{onChange:i=>Ie(e,!0),controls:!1,onClick:l[1]||(l[1]=i=>{i.stopPropagation()}),onStep:l[2]||(l[2]=oa(i=>i.stopPropagation(),["prevent","stop"])),class:"input_deal_wrap",defaultValue:s.default,value:t.value[s.name],"onUpdate:value":i=>t.value[s.name]=i,size:"small",formatter:i=>`${i*100}%`,parser:i=>parseFloat(i.replace("%",""))/100,min:0,max:1},null,8,["onChange","defaultValue","value","onUpdate:value","formatter","parser"])],512),[[O,X.value[e]]])])],10,Qa))),256))]),tt,a("div",lt,[st,a("div",null,[c(V,{size:"small",value:t.value.algorithm,"onUpdate:value":l[3]||(l[3]=s=>t.value.algorithm=s),"button-style":"solid"},{default:r(()=>[(o(!0),k(q,null,I(C(ka)(),s=>(o(),f(m,{value:s.default},{default:r(()=>[R(z(s.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["value"])])])])]),a("div",nt,[ot,a("div",it,[a("div",ut,[a("div",rt,[a("div",{class:A(["s_t_item_sp",{s_t_item_sel:S.value[0]}]),onClick:l[4]||(l[4]=s=>te(0))},_t,2),a("div",{class:A(["s_t_item_sp",{s_t_item_sel:S.value[1]}]),onClick:l[5]||(l[5]=s=>te(1))},mt,2)]),a("div",{class:A(["s_t_item",{s_t_item_sel:S.value[2]}]),onClick:l[6]||(l[6]=s=>te(2))},gt,2),a("div",{class:A(["s_t_item",{s_t_item_sel:S.value[3]}]),onClick:l[7]||(l[7]=s=>te(3))},wt,2),a("div",{class:A(["s_t_item",{s_t_item_sel:S.value[4]}]),onClick:l[8]||(l[8]=s=>te(4))},Ct,2),a("div",{class:A(["s_t_item",{s_t_item_sel:S.value[5]}]),onClick:l[9]||(l[9]=s=>te(5))},zt,2)]),a("div",Ut,[c(be,{activeKey:pe.value,"onUpdate:activeKey":l[23]||(l[23]=s=>pe.value=s)},{default:r(()=>[(o(!0),k(q,null,I(qe.value,s=>(o(),f(ge,{key:s.key,tab:s.title},{default:r(()=>[B(a("div",St,[a("div",Vt,[$t,a("div",Dt,[c(x,{type:"primary",size:"small",onClick:l[10]||(l[10]=e=>F.value.open())},{default:r(()=>[R("请选择")]),_:1}),a("div",Rt,z(_e.value.desc),1)])]),a("div",Lt,[Tt,a("div",Pt,[c(x,{type:"primary",size:"small",onClick:l[11]||(l[11]=e=>se.value.open())},{default:r(()=>[R("请选择")]),_:1}),a("div",Kt,z(ve.value.desc),1)])]),a("div",jt,[Et,a("div",Wt,[c(u,{class:"input_deal_wrap2",defaultValue:0,value:t.value.pv_min_capacity,"onUpdate:value":l[12]||(l[12]=e=>t.value.pv_min_capacity=e),size:"small",min:0},null,8,["value"]),At,c(u,{class:"input_deal_wrap2",value:t.value.pv_max_capacity,"onUpdate:value":l[13]||(l[13]=e=>t.value.pv_max_capacity=e),size:"small",min:0},null,8,["value"])])])],512),[[O,s.key===0]]),B(a("div",Ft,[a("div",Mt,[Nt,a("div",Bt,[c(x,{type:"primary",size:"small",onClick:l[14]||(l[14]=e=>J.value.open())},{default:r(()=>[R("请选择")]),_:1}),a("div",Ot,z(Q.value.desc),1)])]),a("div",Gt,[Zt,a("div",Ht,[c(u,{class:"input_deal_wrap2",value:t.value.wind_min_capacity,"onUpdate:value":l[15]||(l[15]=e=>t.value.wind_min_capacity=e),size:"small",min:0},null,8,["value"]),Jt,c(u,{class:"input_deal_wrap2",value:t.value.wind_max_capacity,"onUpdate:value":l[16]||(l[16]=e=>t.value.wind_max_capacity=e),size:"small",min:0},null,8,["value"])])])],512),[[O,s.key===1]]),B(a("div",null,[a("div",Qt,[(o(!0),k(q,null,I(C(Ia)(),e=>(o(),f(g,{class:"line_form_item",label:e.unit?`${e.label}(${e.unit})`:e.label,name:e.name,rules:e.rules},{default:r(()=>[e.type==="number"&&e.numberType==="ratio"?(o(),f(u,{key:0,class:"input_deal_wrap",defaultValue:e.default,value:t.value[e.name],"onUpdate:value":i=>t.value[e.name]=i,size:"small",formatter:i=>`${i*100}%`,parser:i=>parseFloat(i.replace("%",""))/100,min:0,max:1,step:.01},null,8,["defaultValue","value","onUpdate:value","formatter","parser"])):D("",!0),e.type==="number"&&e.numberType!=="ratio"?(o(),f(u,{key:1,class:"input_deal_wrap",defaultValue:e.default,value:t.value[e.name],"onUpdate:value":i=>t.value[e.name]=i,size:"small",min:0,max:e.max},null,8,["defaultValue","value","onUpdate:value","max"])):e.type==="select"?(o(),f(Y,{key:2,ref_for:!0,ref:"select",value:t.value[e.name],"onUpdate:value":i=>t.value[e.name]=i,style:{width:"120px"},onChange:n.handleChange},{default:r(()=>[(o(!0),k(q,null,I(e.options,i=>(o(),f($,{value:i.value},{default:r(()=>[R(z(i.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value","onChange"])):D("",!0)]),_:2},1032,["label","name","rules"]))),256)),c(g,{class:"line_form_item",label:"网购电价",name:"item.name",rules:"item.rules"},{default:r(()=>{var e,i,ie,ue,re;return[a("div",Xt,[c(fe,{value:n.value,"onUpdate:value":l[17]||(l[17]=ea=>n.value=ea),placeholder:"Please select",options:ze.value,onChange:We},{default:r(()=>[Yt]),_:1},8,["value","options"]),(e=me.value)!=null&&e[1]?(o(),k("span",el,z((ie=(i=me.value)==null?void 0:i[0])==null?void 0:ie.label)+", "+z((re=(ue=me.value)==null?void 0:ue[1])==null?void 0:re.label),1)):D("",!0)])]}),_:1})])],512),[[O,s.key===2]]),B(a("div",null,[a("div",al,[(o(!0),k(q,null,I(C(qa)(),e=>(o(),f(g,{class:"line_form_item",label:e.unit?`${e.label}(${e.unit})`:e.label,name:e.name,rules:e.rules},{default:r(()=>[e.type==="number"?(o(),f(u,{key:0,class:"input_deal_wrap",defaultValue:e.default,value:t.value[e.name],"onUpdate:value":i=>t.value[e.name]=i,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):e.type==="select"?(o(),f(Y,{key:1,size:"small",ref_for:!0,ref:"select",value:t.value[e.name],"onUpdate:value":i=>t.value[e.name]=i,style:{width:"100px"},onChange:n.handleChange},{default:r(()=>[(o(!0),k(q,null,I(e.options,i=>(o(),f($,{value:i.value},{default:r(()=>[R(z(i.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value","onChange"])):D("",!0)]),_:2},1032,["label","name","rules"]))),256)),c(g,{class:"line_form_item",label:"容量范围(MW)"},{default:r(()=>[a("div",tl,[c(u,{class:"input_deal_wrap2",value:t.value.es_min_capacity,"onUpdate:value":l[18]||(l[18]=e=>t.value.es_min_capacity=e),size:"small",min:0},null,8,["value"]),ll,c(u,{class:"input_deal_wrap2",value:t.value.es_max_capacity,"onUpdate:value":l[19]||(l[19]=e=>t.value.es_max_capacity=e),size:"small",min:0},null,8,["value"])])]),_:1})]),a("div",sl,[nl,c(le,{class:"i_val",rowKey:"id","row-selection":{selectedRowKeys:h.selectedBatRowKeys,onChange:Fe,type:"radio"},columns:C(za)(),"data-source":U.value.bat,pagination:!1,size:"small"},null,8,["row-selection","columns","data-source"])])],512),[[O,s.key===3]]),B(a("div",null,[a("div",ol,[(o(!0),k(q,null,I(C(xa)(),e=>(o(),f(g,{class:"line_form_item",label:e.unit?`${e.label}(${e.unit})`:e.label,name:e.name,rules:e.rules},{default:r(()=>[e.type==="number"?(o(),f(u,{key:0,class:"input_deal_wrap",defaultValue:e.default,value:t.value[e.name],"onUpdate:value":i=>t.value[e.name]=i,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):e.type==="select"?(o(),f(Y,{key:1,ref_for:!0,ref:"select",value:t.value[e.name],"onUpdate:value":i=>t.value[e.name]=i,style:{width:"120px"},onChange:n.handleChange},{default:r(()=>[(o(!0),k(q,null,I(e.options,i=>(o(),f($,{value:i.value},{default:r(()=>[R(z(i.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value","onChange"])):D("",!0)]),_:2},1032,["label","name","rules"]))),256)),c(g,{class:"line_form_item",label:"容量范围(MW)"},{default:r(()=>[a("div",il,[c(u,{class:"input_deal_wrap2",value:t.value.ele_min_capacity,"onUpdate:value":l[20]||(l[20]=e=>t.value.ele_min_capacity=e),size:"small",min:0},null,8,["value"]),ul,c(u,{class:"input_deal_wrap2",value:t.value.ele_max_capacity,"onUpdate:value":l[21]||(l[21]=e=>t.value.ele_max_capacity=e),size:"small",min:0},null,8,["value"])])]),_:1})]),a("div",rl,[dl,c(le,{class:"i_val",rowKey:"id","row-selection":{selectedRowKeys:h.selectedAlkRowKeys,onChange:Ke},columns:C(Ca)(),"data-source":U.value.alk,pagination:!1,size:"small"},{bodyCell:r(({column:e,record:i,text:ie})=>{var ue;return[e.key==="type"?(o(),f(ye,{key:0,color:(ue=C(Le)(e.options,ie))==null?void 0:ue.color},{default:r(()=>{var re;return[R(z((re=C(Le)(e.options,ie))==null?void 0:re.label),1)]}),_:2},1032,["color"])):D("",!0)]}),_:1},8,["row-selection","columns","data-source"])])],512),[[O,s.key===4]]),B(a("div",null,[a("div",cl,[(o(!0),k(q,null,I(C(Ua)(),e=>(o(),f(g,{class:"line_form_item",label:e.unit?`${e.label}(${e.unit})`:e.label,name:e.name,rules:e.rules},{default:r(()=>[e.type==="number"?(o(),f(u,{key:0,class:"input_deal_wrap",defaultValue:e.default,value:t.value[e.name],"onUpdate:value":i=>t.value[e.name]=i,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):e.type==="select"?(o(),f(Y,{key:1,ref_for:!0,ref:"select",value:t.value[e.name],"onUpdate:value":i=>t.value[e.name]=i,style:{width:"120px"},onChange:n.handleChange},{default:r(()=>[(o(!0),k(q,null,I(e.options,i=>(o(),f($,{value:i.value},{default:r(()=>[R(z(i.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value","onChange"])):D("",!0)]),_:2},1032,["label","name","rules"]))),256))]),a("div",_l,[a("div",vl,[pl,a("div",ml,[c(x,{type:"primary",size:"small",onClick:l[22]||(l[22]=e=>Ue.value.open())},{default:r(()=>[R("请选择")]),_:1}),a("div",fl,z($e.value.desc),1)])])]),a("div",yl,[gl,c(le,{class:"i_val",rowKey:"id","row-selection":{selectedRowKeys:h.selectedAlkStoreRowKeys,onChange:Be,type:"radio"},columns:C(Sa)(),"data-source":U.value.alkStorage,pagination:!1,size:"small"},null,8,["row-selection","columns","data-source"])])],512),[[O,s.key===5]])]),_:2},1032,["tab"]))),128))]),_:1},8,["activeKey"])])])]),a("div",bl,[hl,a("div",wl,[c(N,{ghost:"",activeKey:j.value,"onUpdate:activeKey":l[24]||(l[24]=s=>j.value=s),style:{background:"#fff"}},{default:r(()=>[(o(),f(ee,{key:1,header:"投资成本"},{default:r(()=>[a("div",kl,[(o(!0),k(q,null,I(C(Va)(),s=>(o(),f(g,{class:"line_form_item",label:s.unit?`${s.label}(${s.unit})`:s.label,name:s.name,rules:s.rules},{default:r(()=>[s.type==="number"?(o(),f(u,{key:0,class:"input_deal_wrap",defaultValue:s.default,value:t.value[s.name],"onUpdate:value":e=>t.value[s.name]=e,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):D("",!0)]),_:2},1032,["label","name","rules"]))),256))])]),_:1})),(o(),f(ee,{key:2,header:"运营成本"},{default:r(()=>[a("div",xl,[(o(!0),k(q,null,I(C($a)(),s=>(o(),f(g,{class:"line_form_item",label:s.unit?`${s.label}(${s.unit})`:s.label,name:s.name,rules:s.rules},{default:r(()=>[s.type==="number"&&s.numberType==="ratio"?(o(),f(u,{key:0,class:"input_deal_wrap",defaultValue:s.default,value:t.value[s.name],"onUpdate:value":e=>t.value[s.name]=e,size:"small",formatter:e=>`${e*100}%`,parser:e=>parseFloat(e.replace("%",""))/100,min:0,max:1,step:.01},null,8,["defaultValue","value","onUpdate:value","formatter","parser"])):D("",!0),s.type==="number"&&s.numberType!=="ratio"?(o(),f(u,{key:1,class:"input_deal_wrap",defaultValue:s.default,value:t.value[s.name],"onUpdate:value":e=>t.value[s.name]=e,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):D("",!0)]),_:2},1032,["label","name","rules"]))),256))])]),_:1}))]),_:1},8,["activeKey"])])])]),_:1},8,["model"])]),_:1},8,["spinning"])])]),a("div",Cl,[c(x,{onClick:Ze,type:"primary",block:"",loading:h.running},{default:r(()=>[R("运行")]),_:1},8,["loading"])]),c(we,{chartList:M.value,config:{modalTitle:"光伏出力曲线",hasZoom:!0,canUploadSelf:!0,default:{id:t.value.pv_forecast_zone_id}},ref_key:"PVPowerModalRef",ref:F,onSubmit:Ce},null,8,["chartList","config"]),c(we,{chartList:xe.value,selfData:ne.value,config:{modalTitle:"光伏设备衰减曲线",hasZoom:!1,canUploadSelf:!1},ref_key:"PVDeviceModalRef",ref:se,onSubmit:L},null,8,["chartList","selfData"]),c(we,{chartList:P.value,config:{modalTitle:"风机出力曲线",hasZoom:!0,canUploadSelf:!0,default:{id:t.value.wind_forecast_zone_id}},ref_key:"WindPowerModalRef",ref:J,onSubmit:ae},null,8,["chartList","config"]),c(we,{chartList:Ve.value,config:{modalTitle:"需氢曲线",hasZoom:!0,canUploadSelf:!1,default:{id:t.value.absorb_id}},ref_key:"H2ConsumeModalRef",ref:Ue,onSubmit:Ne},null,8,["chartList","config"])])}}},Dl=Te(Il,[["__scopeId","data-v-ab12b5e2"]]);export{Dl as default};
