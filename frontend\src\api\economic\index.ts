// import { AxiosPromise} from 'axios'
import { get, post, urlPrefix, type Res, del } from '../index'

export interface TaskItem {
  [key: string]: any // TODO
}

export interface PolicyItem {
  id: number;
  name: string;
  status: number; // 0 未启用；1 启用
  desc: string;
  // 以下暂时自定义
  score: number;
  creator: string; // 定制人
  period: string; // 定制周期
}

export interface PlanItem {
  id: number;
  dev_id: number; // 1-6. 1-4 ALK, 5-PV, 6-SOC, 8-Load
  prop_id: number;
  type: number;
  interval: number;
  data: number[];
  begin_time: string;
  end_time: string;
  create_time: string;
  desc: string;
}

export const getEconomicProject = async (params: any): Promise<Res<any[]>> => {
  return await get(`${urlPrefix}/economic/getProject`, params);
}

export const getEconomicSolutionInfo = async (params: any): Promise<Res<any[]>> => {
  return await get(`${urlPrefix}/economic/getSolution`, params);
}

export const modifyEconomicProject = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await post(`${urlPrefix}/economic/modifyProject`, params);
}

export const modifyEconomicSolutionInfo = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await post(`${urlPrefix}/economic/modifySolution`, params);
}


export const getEconomicSolutionResult = async (params: any): Promise<Res<any[]>> => {
  return await get(`${urlPrefix}/economic/getResult`, params);
}

export const submitEconomicTask = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await post(`${urlPrefix}/economic/submitTask`, params);
}

export const getEconomicCompareResult = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await post(`${urlPrefix}/economic/compareSolutions`, params);
}

