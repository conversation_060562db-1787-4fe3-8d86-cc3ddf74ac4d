<template>
  <a-layout-sider
    width="200"
    collapsible
    theme="light"
    v-model:collapsed="collapsed"
    :style="{ overflow: 'auto', height: 'calc(100vh - 64px)', background: '#fff' }"
  >
    <a-menu
      mode="inline"
      v-model:openKeys="state.openKeys"
      v-model:selectedKeys="state.selectedKeys"
      :items="MenuItems()"
      @click="navTo">
    </a-menu>
  </a-layout-sider>
</template>

<script lang="ts" setup>

import { reactive, watch, onMounted, ref } from 'vue';
// import { useBaseStore } from '@/stores/base'
import { useRouter, useRoute } from 'vue-router';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons-vue';
import MenuItems from './menuConfig';

const props = defineProps<{
  inlineCollapsed: boolean
}>()
const collapsed = ref(false)
const router = useRouter();
const route = useRoute()
// const baseStore = useBaseStore()

const state = reactive({
  collapsed: false,
  selectedKeys: ['monitor'],
  openKeys: ['monitorPage'],
  // preOpenKeys: ['2'],
});

const navTo = (route: { key: string }) => {
  console.log('r:', route)
  router.push({
    name: route.key,
  });
};


watch(
  () => state.openKeys,
  (_val, oldVal) => {
    // state.preOpenKeys = oldVal;

  },
);

watch(route, (newRoute) => {
  // console.log('route change:', newRoute)
  state.selectedKeys = [newRoute.meta.key]
  if (newRoute.name == 'preview') {
    baseStore.setMainLayoutStyle({ padding: 0, background: '#E2E5EA' })
  } else {
    baseStore.setMainLayoutStyle({})
  }
}, { deep: true })


const toggleCollapsed = () => {
  // state.collapsed = !state.collapsed;
  baseStore.collapseMenu()
  state.openKeys = baseStore.menuCollapsed ? [] : state.preOpenKeys;
};

onMounted(() => {
  state.selectedKeys = [route.meta.key]
  if (baseStore.menuCollapsed) {
    state.openKeys = []
  }
})
</script>

<style scoped lang="less">

</style>
  
  