import{u as se,l as oe,h as re,n as ie}from"./index-BbGSYVSM.js";import{_ as ue,M as F,u as de,r as _,o as ce,d as u,b as s,c as b,e as o,f as m,w as n,A as $,l as f,j as w,h as C,k as q,t as V,F as L,i as j,p as _e,m as pe}from"./index-Blktg-U-.js";import{l as T}from"./lodash-DsxhgM2J.js";/* empty css                                                              */import{g as W}from"./index-CB8EjQs8.js";const me=()=>[{title:"",dataIndex:""},{title:"项目操作",dataIndex:"action",key:"action"}],fe=()=>[{title:"",dataIndex:""},{title:"项目操作",dataIndex:"action",key:"action"}],z=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:""},{title:"单机功率(MW)",dataIndex:"single_power",s_type:"number",rules:[{required:!1}],unit:""},{title:"充电效率",dataIndex:"charge_efficiency",s_type:"number",rules:[{required:!1}],unit:""},{title:"放电效率",dataIndex:"discharge_efficiency",s_type:"number",rules:[{required:!1}],unit:""},{title:"充放电倍率",dataIndex:"c_rate",s_type:"number",rules:[{required:!1}],unit:""},{title:"储能占比",dataIndex:"radio",s_type:"number",rules:[{required:!1}],unit:""},{title:"初始SOC",dataIndex:"init_soc",s_type:"number",rules:[{required:!1}],unit:""},{title:"SOC下限",dataIndex:"min_soc",s_type:"number",rules:[{required:!1}],unit:""},{title:"SOC上限",dataIndex:"max_soc",s_type:"number",rules:[{required:!1}],unit:""},{title:"置信度",dataIndex:"confidence",s_type:"number",rules:[{required:!1}],unit:""},{title:"寿命(年)",dataIndex:"life_cycle",s_type:"number",rules:[{required:!1}],unit:""},{title:"项目操作",dataIndex:"action",key:"action"}],G=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:"",width:"80px"},{title:"类型",dataIndex:"type",key:"type",s_type:"select",rules:[{required:!1}],unit:"",options:[{label:"ALK",value:1,color:"blue"},{label:"PEM",value:2,color:"green"}]},{title:"容量(Nm³/h)",dataIndex:"capacity",s_type:"number",rules:[{required:!1}],unit:""},{title:"能耗(kwh/Nm³)",dataIndex:"power_consumption",s_type:"number",rules:[{required:!1}],unit:""},{title:"额定功率(MW)",dataIndex:"pe",s_type:"number",rules:[{required:!1}],unit:""},{title:"最低负载率",dataIndex:"lower_load_rate",s_type:"number",rules:[{required:!1}],unit:""},{title:"最高负载率",dataIndex:"upper_load_rate",s_type:"number",rules:[{required:!1}],unit:""},{title:"辅助系统能耗(kwh/Nm³)",dataIndex:"assist_consumption",s_type:"number",rules:[{required:!1}],unit:""},{title:"单价(元)",dataIndex:"price",s_type:"number",rules:[{required:!1}],unit:""},{title:"制氢电源效率",dataIndex:"power_supply_efficiency",s_type:"number",rules:[{required:!1}],unit:""},{title:"项目操作",dataIndex:"action",key:"action",width:"100px"}],H=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:""},{title:"体积(m³)",dataIndex:"volume",s_type:"number",rules:[{required:!1}],unit:""},{title:"最低运行压力(Pa)",dataIndex:"min_pressure",s_type:"number",rules:[{required:!1}],unit:""},{title:"最大运行压力(Pa)",dataIndex:"max_pressure",s_type:"number",rules:[{required:!1}],unit:""},{title:"价格(元)",dataIndex:"price",s_type:"number",rules:[{required:!1}],unit:""},{title:"占地面积(㎡)",dataIndex:"area",s_type:"number",rules:[{required:!1}],unit:""},{title:"项目操作",dataIndex:"action",key:"action"}],J=h=>(_e("data-v-268b35ed"),h=h(),pe(),h),ve={class:"body_wrap"},ye={class:"p_wrap"},be=J(()=>o("div",{class:"title_wrap"},null,-1)),xe={class:"content_wrap",id:"content_wrap"},Ie={class:"part_wrap"},ge=J(()=>o("div",{class:"p_title"},[o("div",null,"设备配置"),o("div",{class:"btn_wrap"})],-1)),ke={class:"tab_wrap"},we={key:0,class:"t_btn_wrap"},Ce=["onClick"],qe=["onClick"],he={key:0,class:"t_btn_wrap"},De=["onClick"],Ee=["onClick"],Ne={key:0,class:"t_btn_wrap"},Se=["onClick"],$e=["onClick"],Ke={__name:"index",setup(h){F.useModal(),de();const x=_(!1),I=_(!1),A=_(),g=_([]),d=_({}),O=_({}),K=_(!1),k=_(3);_([]);const D=_(!1),E=_(z().filter(a=>a.s_type)),R=a=>[me,fe,function(){return[]},z,G,H][a]().filter(r=>r.s_type),M=(a,t)=>{F.confirm({title:"确认删除?",async onOk(){const{code:r,msg:c}=await ie({devIdList:[a.id]});r===0?($.success("删除成功"),N()):$.error(c)}})},P=(a,t)=>{I.value=!0,D.value=!0,E.value=R(t),O.value=a,d.value=T.cloneDeep(a)},U=a=>{I.value=!0,D.value=!1,E.value=R(a),console.log("form item:",E.value,a)},Q=a=>{k.value=a,N()},X=async()=>{var v;await A.value.validateFields();const a=k.value,t={manufacturer:d.value.manufacturer,model:d.value.model};let r,c;K.value=!0;const i=T.cloneDeep(d.value);if(delete i.manufacturer,delete i.model,delete i.id,delete i.category,D.value){t.id=(v=O.value)==null?void 0:v.id;const p=await se([{type:a,baseInfo:t,params:i}]);r=p.code,c=p.msg}else{const p=await oe([{type:a,baseInfo:t,params:i}]);r=p.code,c=p.msg}K.value=!1,r===0?N():$.error(c),I.value=!1},N=async a=>{x.value=!0;const{code:t,data:r,msg:c}=await re({type:k.value});t===0?(g.value=r.result.map(i=>{const{baseInfo:v,params:p}=i;return{...v,...p}}),console.log("table:",g.value)):$.error(c),x.value=!1};return ce(()=>{N()}),(a,t)=>{const r=u("a-button"),c=u("a-table"),i=u("a-tab-pane"),v=u("a-tag"),p=u("a-tabs"),Y=u("a-input-number"),Z=u("a-input"),ee=u("a-select-option"),te=u("a-select"),ae=u("a-form-item"),le=u("a-form"),ne=u("a-modal");return s(),b("div",ve,[o("div",ye,[be,o("div",xe,[o("div",Ie,[ge,o("div",ke,[m(p,{activeKey:k.value,"onUpdate:activeKey":t[3]||(t[3]=e=>k.value=e),onChange:Q},{default:n(()=>[(s(),f(i,{key:3,tab:"储能"},{default:n(()=>[m(r,{style:{margin:"-10px 0 10px 0"},size:"small",onClick:t[0]||(t[0]=e=>U(3))},{default:n(()=>[w("新增")]),_:1}),m(c,{size:"small",loading:x.value,pagination:!1,columns:C(z)(),rowKey:"id","data-source":g.value,defaultExpandAllRows:!0},{bodyCell:n(({column:e,record:l})=>[e.key==="action"?(s(),b("div",we,[o("a",{class:"a_item",onClick:y=>P(l,3)},"修改",8,Ce),o("a",{class:"a_item",onClick:y=>M(l,3)},"删除",8,qe)])):q("",!0)]),_:1},8,["loading","columns","data-source"])]),_:1})),(s(),f(i,{key:4,tab:"电解槽"},{default:n(()=>[m(r,{style:{margin:"-10px 0 10px 0"},size:"small",onClick:t[1]||(t[1]=e=>U(4))},{default:n(()=>[w("新增")]),_:1}),m(c,{size:"small",loading:x.value,pagination:!1,columns:C(G)(),rowKey:"id","data-source":g.value,defaultExpandAllRows:!0},{bodyCell:n(({column:e,record:l,text:y})=>{var B;return[e.key==="action"?(s(),b("div",he,[o("a",{href:"void:0",class:"a_item",onClick:S=>P(l,4)},"修改",8,De),o("a",{href:"void:0",class:"a_item",onClick:S=>M(l,4)},"删除",8,Ee)])):q("",!0),e.key==="type"?(s(),f(v,{key:1,color:(B=C(W)(e.options,y))==null?void 0:B.color},{default:n(()=>{var S;return[w(V((S=C(W)(e.options,y))==null?void 0:S.label),1)]}),_:2},1032,["color"])):q("",!0)]}),_:1},8,["loading","columns","data-source"])]),_:1})),(s(),f(i,{key:5,tab:"储罐"},{default:n(()=>[m(r,{style:{margin:"-10px 0 10px 0"},size:"small",onClick:t[2]||(t[2]=e=>U(5))},{default:n(()=>[w("新增")]),_:1}),m(c,{size:"small",loading:x.value,pagination:!1,columns:C(H)(),rowKey:"id","data-source":g.value,defaultExpandAllRows:!0},{bodyCell:n(({column:e,record:l})=>[e.key==="action"?(s(),b("div",Ne,[o("a",{href:"void:0",class:"a_item",onClick:y=>P(l,5)},"修改",8,Se),o("a",{href:"void:0",class:"a_item",onClick:y=>M(l,5)},"删除",8,$e)])):q("",!0)]),_:1},8,["loading","columns","data-source"])]),_:1}))]),_:1},8,["activeKey"])])])])]),m(ne,{open:I.value,"onUpdate:open":t[4]||(t[4]=e=>I.value=e),title:D.value?"修改设备":"创建设备",onOk:X,"confirm-loading":K.value,destroyOnClose:""},{default:n(()=>[o("div",null,[m(le,{labelAlign:"left2",ref_key:"formRef",ref:A,model:d.value,name:"basic","label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:n(()=>[(s(!0),b(L,null,j(E.value,e=>(s(),f(ae,{label:e.title,name:e.dataIndex,rules:e.rules},{default:n(()=>[e.s_type==="number"?(s(),f(Y,{key:0,style:{width:"100%"},value:d.value[e.dataIndex],"onUpdate:value":l=>d.value[e.dataIndex]=l,size:"small",min:0},null,8,["value","onUpdate:value"])):e.s_type==="string"?(s(),f(Z,{key:1,value:d.value[e.dataIndex],"onUpdate:value":l=>d.value[e.dataIndex]=l},null,8,["value","onUpdate:value"])):e.s_type==="select"?(s(),f(te,{key:2,value:d.value[e.dataIndex],"onUpdate:value":l=>d.value[e.dataIndex]=l,style2:"width: 120px"},{default:n(()=>[(s(!0),b(L,null,j(e.options,l=>(s(),f(ee,{value:l.value},{default:n(()=>[w(V(l.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value"])):q("",!0)]),_:2},1032,["label","name","rules"]))),256))]),_:1},8,["model"])])]),_:1},8,["open","title","confirm-loading"])])}}},Oe=ue(Ke,[["__scopeId","data-v-268b35ed"]]);export{Oe as default};
