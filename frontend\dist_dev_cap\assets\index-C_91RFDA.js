import{N as v,r as g,u as b,x as h,d as o,b as x,c as y,e as d,f as e,w as t,j as I,O as N,A as S,p as B,m as C,_ as V}from"./index-Blktg-U-.js";const k=n=>(B("data-v-27d45ec7"),n=n(),C(),n),q={class:"bg_wrap"},U={class:"login_window"},j=k(()=>d("div",{class:"title"},"综合能源规划设计系统",-1)),A=v({__name:"index",setup(n){const r=g(!1),m=b(),a=h({username:"",password:""}),_=async c=>{r.value=!0;const{code:s,msg:l}=await N(c);r.value=!1,s===0?(console.log("login success"),m.push({name:"home"})):S.error(l)};return(c,s)=>{const l=o("a-input"),p=o("a-form-item"),i=o("a-input-password"),f=o("a-button"),w=o("a-form");return x(),y("div",q,[d("div",U,[j,e(w,{class:"form_wrap",model:a,name:"basic","label-col":{span:6},"wrapper-col":{span:14},autocomplete:"off",onFinish:_},{default:t(()=>[e(p,{label:"用户名",name:"username",rules:[{required:!0,message:"请输入用户名!"}]},{default:t(()=>[e(l,{value:a.username,"onUpdate:value":s[0]||(s[0]=u=>a.username=u)},null,8,["value"])]),_:1}),e(p,{label:"密码",name:"password",rules:[{required:!0,message:"请输入密码!"}]},{default:t(()=>[e(i,{value:a.password,"onUpdate:value":s[1]||(s[1]=u=>a.password=u)},null,8,["value"])]),_:1}),e(p,{"wrapper-col":{offset:6,span:14}},{default:t(()=>[e(f,{loading:r.value,style:{width:"100%"},type:"primary","html-type":"submit"},{default:t(()=>[I(" 登录 ")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])])])}}}),F=V(A,[["__scopeId","data-v-27d45ec7"]]);export{F as default};
