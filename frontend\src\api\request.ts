import axios from "axios";
import { message } from 'ant-design-vue';

const request = axios.create({
  // baseURL: process.env.VUE_APP_API_BASE, // 域名配置，可添加变量配置文件定义
  // baseURL: 'http://10.40.19.136:10180',
  baseURL: '/',

  headers: {
    "Content-Type": "application/json;charset=utf-8",
  },
  withCredentials: true, // 跨域请求时是否需要使用凭证
  timeout: 600000, // 请求超时时间
  // transformResponse: [
  //   data => {
  //     // console.log('axios data:', data)
  //     return JSON.parse(data);
  //   }
  // ]
});

// 错误处理函数
function errorHandle(data: any) {
  const res = data?.response
  if (res?.status > 400 && res?.status < 600) {
    message.error(res?.statusText)
  }
}
// 成功处理函数

function successHandle(response: any) {
  if (response.data?.code === 5 && location.pathname !== '/login') {
    // console.log('需要登录:', response, location)
    message.warn('请先登录')
    window.location.href = "/login"
  }
  if (response.status >= 200 && response.status < 400) {
    return {
      ...response.data,
      data: response.data?.data || {}
    };
  }
}
// 请求拦截器
request.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    // 错误抛到业务代码
    error.data = {};
    error.data.msg = "服务器异常";
    return Promise.resolve(error);
  }
);

request.interceptors.response.use(
  (response: any) => {
    return successHandle(response);
  },
  (err) => {
    errorHandle(err);
  }
);

export default request;


