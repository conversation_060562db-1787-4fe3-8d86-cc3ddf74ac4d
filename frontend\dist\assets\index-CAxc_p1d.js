import{l as qe,n as Te,i as Re,u as ze,o as De,p as Me,e as Pe,q as Ee}from"./index-B1fCd-lN.js";import{_ as oe,r as k,z as Be,e as X,H as ee,o as le,f as c,b as m,m as E,w as l,h as r,n as h,v as C,L as te,B as Ae,c as K,g as d,i as j,P as de,t as J,K as Ue,p as W,F as ne,l as ae,A as Q,E as ge,G as ye,M as pe,d as Ke}from"./index-C98MXVsn.js";import{i as Ne,l as _e}from"./lodash-BSye1qWC.js";/* empty css                                                              */import{g as fe}from"./index-BEuFRmea.js";import"./xlsx-D5JNrnKm.js";const Ye=()=>[{title:"",dataIndex:""},{title:"操作",dataIndex:"action",key:"action"}],Ze=()=>[{title:"",dataIndex:""},{title:"操作",dataIndex:"action",key:"action"}],ce=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:""},{title:"单机功率(MW)",dataIndex:"single_power",s_type:"number",rules:[{required:!1}],unit:""},{title:"充电效率",dataIndex:"charge_efficiency",s_type:"number",rules:[{required:!1}],unit:""},{title:"放电效率",dataIndex:"discharge_efficiency",s_type:"number",rules:[{required:!1}],unit:""},{title:"充放电倍率",dataIndex:"c_rate",s_type:"number",rules:[{required:!1}],unit:""},{title:"初始SOC",dataIndex:"init_soc",s_type:"number",rules:[{required:!1}],unit:""},{title:"SOC下限",dataIndex:"min_soc",s_type:"number",rules:[{required:!1}],unit:""},{title:"SOC上限",dataIndex:"max_soc",s_type:"number",rules:[{required:!1}],unit:""},{title:"寿命(年)",dataIndex:"life_cycle",s_type:"number",rules:[{required:!1}],unit:""},{title:"操作",dataIndex:"action",key:"action"}],me=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:"",width:"80px"},{title:"类型",dataIndex:"ele_type",key:"ele_type",s_type:"select",rules:[{required:!1}],unit:"",options:[{label:"ALK",value:1,color:"blue"},{label:"PEM",value:2,color:"green"}]},{title:"容量(Nm³/h)",dataIndex:"capacity",s_type:"number",rules:[{required:!1}],unit:""},{title:"能耗(kwh/Nm³)",dataIndex:"power_consumption",s_type:"number",rules:[{required:!1}],unit:""},{title:"额定功率(MW)",dataIndex:"pe",s_type:"number",rules:[{required:!1}],unit:""},{title:"最低负载率",dataIndex:"lower_load_rate",s_type:"number",rules:[{required:!1}],unit:""},{title:"最高负载率",dataIndex:"upper_load_rate",s_type:"number",rules:[{required:!1}],unit:""},{title:"辅助系统能耗(kwh/Nm³)",dataIndex:"assist_consumption",s_type:"number",rules:[{required:!1}],unit:""},{title:"系统价格(元/套)",dataIndex:"price",s_type:"number",rules:[{required:!1}],unit:""},{title:"制氢电源效率",dataIndex:"power_supply_efficiency",s_type:"number",rules:[{required:!1}],unit:""},{title:"操作",dataIndex:"action",key:"action",width:"100px"}],ve=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:""},{title:"体积(m³)",dataIndex:"volume",s_type:"number",rules:[{required:!1}],unit:""},{title:"最小运行压力(Mpa)",dataIndex:"min_pressure",s_type:"number",rules:[{required:!1}],unit:""},{title:"最大运行压力(Mpa)",dataIndex:"max_pressure",s_type:"number",rules:[{required:!1}],unit:""},{title:"价格(元)",dataIndex:"price",s_type:"number",rules:[{required:!1}],unit:""},{title:"占地面积(㎡)",dataIndex:"area",s_type:"number",rules:[{required:!1}],unit:""},{title:"操作",dataIndex:"action",key:"action"}],He={__name:"RegionModal",props:{visible:Boolean,existingRegions:{type:Array,default:()=>[]}},emits:["update:visible","confirm"],setup(A,{emit:U}){const M=A,g=U,S=k({}),b=k(),s=Be({regionType:"existing",province:"",city:"",customRegion:"",period:"year",cascaderValue:[],selectedZoneInfo:null}),w={regionType:[{required:!0,message:"请选择地区类型"}],province:[{required:!0,message:"请选择省份"}],city:[{required:!0,message:"请选择城市"}],customRegion:[{required:!0,message:"请输入地区名称"}],period:[{required:!0,message:"请选择电价区间"}]},P=X(()=>S.value.result.map(y=>({value:y.province,label:y.province,children:y.subZones.map(_=>({value:_.city,label:_.city,zoneId:_.id,region:_.region,country:_.country}))}))),$=()=>{s.province="",s.city="",s.customRegion="",s.cascaderValue=[],s.selectedZoneInfo=null},H=(y,_)=>{y&&y.length===2&&_&&_.length===2?(s.province=y[0],s.city=y[1],s.selectedZoneInfo={zoneId:_[1].zoneId,region:_[1].region,country:_[1].country}):(s.province="",s.city="",s.selectedZoneInfo=null)},N=async()=>{var y,_,u;try{await b.value.validate();let v={period:s.period};if(s.regionType==="existing"){if(M.existingRegions.some(p=>p.province===s.province&&p.city===s.city)){C.error("该地区已存在，不能重复添加");return}v={...v,zoneId:((y=s.selectedZoneInfo)==null?void 0:y.zoneId)||Date.now(),region:((_=s.selectedZoneInfo)==null?void 0:_.region)||"",country:((u=s.selectedZoneInfo)==null?void 0:u.country)||"中国",province:s.province,city:s.city,groupedTouPriceByMonth:[]}}else{if(M.existingRegions.some(p=>p.province===s.customRegion&&!p.city)){C.error("该自定义地区已存在，不能重复添加");return}v={...v,zoneId:null,region:"自定义地区",country:"中国",province:s.customRegion,city:"",groupedTouPriceByMonth:[]}}g("confirm",v),B()}catch(v){console.log("表单验证失败:",v)}},B=()=>{var y;g("update:visible",!1),(y=b.value)==null||y.resetFields(),Object.assign(s,{regionType:"existing",province:"",city:"",customRegion:"",period:"year",cascaderValue:[],selectedZoneInfo:null})},T=async()=>{const{code:y,msg:_,data:u}=await qe();y===0?S.value=u:C.error(_)};return ee(()=>M.visible,y=>{y||B()}),le(async()=>{T()}),(y,_)=>{const u=c("a-radio"),v=c("a-radio-group"),f=c("a-form-item"),p=c("a-cascader"),x=c("a-input"),R=c("a-select-option"),z=c("a-select"),L=c("a-form"),Y=c("a-modal");return m(),E(Y,{visible:A.visible,title:"新增地区",width:500,onOk:N,onCancel:B},{default:l(()=>[r(L,{ref_key:"formRef",ref:b,model:s,rules:w,"label-col":{span:6},"wrapper-col":{span:18}},{default:l(()=>[r(f,{label:"地区类型",name:"regionType"},{default:l(()=>[r(v,{value:s.regionType,"onUpdate:value":_[0]||(_[0]=Z=>s.regionType=Z),onChange:$},{default:l(()=>[r(u,{value:"existing"},{default:l(()=>[h("选择已有地区")]),_:1}),r(u,{value:"custom"},{default:l(()=>[h("自定义地区")]),_:1})]),_:1},8,["value"])]),_:1}),s.regionType==="existing"?(m(),E(f,{key:0,label:"地区选择",name:"province"},{default:l(()=>[r(p,{value:s.cascaderValue,"onUpdate:value":_[1]||(_[1]=Z=>s.cascaderValue=Z),options:P.value,placeholder:"请选择省份/城市","show-search":!0,onChange:H},null,8,["value","options"])]),_:1})):(m(),E(f,{key:1,label:"地区名称",name:"customRegion"},{default:l(()=>[r(x,{value:s.customRegion,"onUpdate:value":_[2]||(_[2]=Z=>s.customRegion=Z),placeholder:"请输入自定义地区名称"},null,8,["value"])]),_:1})),r(f,{label:"电价区间",name:"period"},{default:l(()=>[r(z,{value:s.period,"onUpdate:value":_[3]||(_[3]=Z=>s.period=Z),placeholder:"请选择电价区间"},{default:l(()=>[r(R,{value:"year"},{default:l(()=>[h("年度(整年)")]),_:1}),r(R,{value:"halfYear"},{default:l(()=>[h("半年(上下半年)")]),_:1}),r(R,{value:"quarter"},{default:l(()=>[h("季度(四个季度)")]),_:1}),r(R,{value:"month"},{default:l(()=>[h("月度(12个月)")]),_:1})]),_:1},8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["visible"])}}},Le=oe(He,[["__scopeId","data-v-6d2503c9"]]),Fe={__name:"PriceChart",props:{data:{type:Array,default:()=>[]}},emits:["select"],setup(A,{expose:U,emit:M}){const g=A,S=M,b=k();let s=null;const w={1:"#52c41a",2:"#1890ff",3:"#faad14",4:"#ff7a45",5:"#f5222d"},P={1:"深谷",2:"低谷",3:"平时",4:"高峰",5:"尖峰"},$=()=>{s&&s.resize()},H=()=>{b.value&&(s=Ne(b.value),s.on("click",B=>{S("select",{hour:B.dataIndex,data:B.data})}),N())},N=()=>{if(!s)return;const B=Array.from({length:24},(u,v)=>v),T={};Object.keys(P).forEach(u=>{T[u]={name:P[u],data:new Array(24).fill(null),color:w[u]}}),g.data.forEach(u=>{for(let v=u.begin;v<=u.end;v++)T[u.type].data[v]=u.price});const y=Object.keys(T).filter(u=>T[u].data.some(v=>v!==null)).map(u=>({name:T[u].name,type:"line",data:T[u].data,step:"end",lineStyle:{width:3,color:T[u].color},areaStyle:{color:T[u].color+"30"},symbol:"circle",symbolSize:6,itemStyle:{color:T[u].color},connectNulls:!1,z:parseInt(u)})),_={title:{left:"center",textStyle:{fontSize:16,fontWeight:"normal"}},tooltip:{trigger:"axis",formatter:u=>{const v=u[0].dataIndex,f=g.data.find(x=>v>=x.begin&&v<=x.end),p=u.find(x=>x.value!==null);return`
          <div>
            <div>时间: ${v}:00-${v+1}:00</div>
            <div>类型: ${f?P[f.type]:"未配置"}</div>
            <div>价格: ${p?p.value:0} 元/kWh</div>
          </div>
        `}},legend:{show:!0,top:"8%",data:y.map(u=>({name:u.name,icon:"circle",textStyle:{color:u.lineStyle.color}}))},grid:{top:"20%",left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:B.map(u=>`${u}:00`),axisLabel:{interval:1,rotate:45},boundaryGap:!1},yAxis:{type:"value",name:"价格(元/kWh)",min:0},series:y};s.setOption(_,!0)};return ee(()=>g.data,()=>{te(()=>{N()})},{deep:!0}),le(()=>{te(()=>{H()}),window.addEventListener("resize",$)}),Ae(()=>{window.removeEventListener("resize",$),s&&(s.dispose(),s=null)}),U({updateChart:N,resize:$}),(B,T)=>(m(),K("div",{class:"price-chart",ref_key:"chartRef",ref:b},null,512))}},Ge=oe(Fe,[["__scopeId","data-v-abb34e36"]]),he=A=>(ge("data-v-77efc61c"),A=A(),ye(),A),Oe={class:"grid-price-config"},Ve={class:"main-content"},je={class:"left-panel"},We={class:"panel-header"},Qe=he(()=>d("span",{class:"panel-title"},"地区",-1)),Je={class:"region-tree"},Xe={class:"tree-node-content"},et={class:"node-title"},tt={key:0,class:"node-actions"},nt={class:"right-panel"},at={key:0,class:"config-area"},ot={class:"config-header"},lt={style:{display:"flex","justify-content":"space-between","align-items":"center"}},st={class:"month-tabs"},rt={class:"price-config-section"},it={class:"section-header"},ut=he(()=>d("h4",null,"24小时电价配置",-1)),ct={class:"price-chart-container"},dt={key:1,class:"empty-state"},pt={class:"time-edit-content"},_t={class:"time-periods"},ft={class:"period-title"},mt={__name:"index",setup(A){const U=k(!1),M=k(!1),g=k([]),S=k([]),b=k([]),s=k(0),w=k([]),P=k();k(!1);const $=X(()=>{if(S.value.length===0)return null;const t=S.value[0];return g.value.find(n=>n.id.toString()===t)}),H=X(()=>{const t=new Map,n=[];return g.value.forEach(e=>{const{province:a,city:i,id:I}=e;i?(t.has(a)||t.set(a,{title:a,key:`province-${a}`,children:[],selectable:!1}),t.get(a).children.push({title:i,key:I.toString(),isLeaf:!0,province:a,city:i})):n.push({title:a,key:I.toString(),isLeaf:!0,province:a,city:""})}),[...Array.from(t.values()),...n]}),N=X(()=>{if(!$.value)return[];const{period:t,groupedTouPriceByMonth:n}=$.value;let o=[];switch(t){case"year":o=[{label:"全年(1-12月)",months:[1,2,3,4,5,6,7,8,9,10,11,12],begin:1,end:12}];break;case"halfYear":o=[{label:"上半年(1-6月)",months:[1,2,3,4,5,6],begin:1,end:6},{label:"下半年(7-12月)",months:[7,8,9,10,11,12],begin:7,end:12}];break;case"quarter":o=[{label:"第一季度(1-3月)",months:[1,2,3],begin:1,end:3},{label:"第二季度(4-6月)",months:[4,5,6],begin:4,end:6},{label:"第三季度(7-9月)",months:[7,8,9],begin:7,end:9},{label:"第四季度(10-12月)",months:[10,11,12],begin:10,end:12}];break;case"month":o=Array.from({length:12},(e,a)=>({label:`${a+1}月`,months:[a+1],begin:a+1,end:a+1}));break;default:return[]}return o.map(e=>{const a=n==null?void 0:n.find(q=>q.begin===e.begin&&q.end===e.end),I=((a==null?void 0:a.groupedTouPriceByDay)||[]).map(q=>({type:q.type||3,price:q.price||0,begin:q.begin||0,end:q.end||23}));return{...e,priceData:I,hasData:I.length>0}})}),B=t=>{if(S.value=t,s.value=0,t.length>0){const n=g.value.find(o=>o.id.toString()===t[0]);console.log("选中的地区配置:",n),n&&(console.log("电价数据:",n.groupedTouPriceByMonth),console.log("推断的period类型:",n.period))}},T=t=>{b.value=t},y=t=>{const n=Date.now(),o={...t,id:n,zoneId:t.zoneId,currentYear:new Date().getFullYear().toString(),createTime:Date.now(),isNew:!0};g.value.push(o),S.value=[o.id.toString()],U.value=!1,t.city&&(b.value=[...new Set([...b.value,`province-${t.province}`])]),C.success("地区添加成功，请配置电价后保存")},_=async t=>{const{code:n,data:o,msg:e}=await Te([parseInt(t)]);n===0?(C.success("删除成功"),await Z()):C.error(e)},u=t=>{s.value=t},v=t=>{console.log("选中图表数据:",t)},f=()=>{const t=N.value[s.value];t&&t.priceData&&t.priceData.length>0?w.value=t.priceData.map(n=>({...n,timeRange:[Q().hour(n.begin).minute(0),Q().hour(n.end).minute(0)]})):w.value=[{type:3,price:.5,begin:0,end:23,timeRange:[Q().hour(0).minute(0),Q().hour(23).minute(0)]}],M.value=!0},p=()=>{const t=new Set;w.value.forEach(e=>{for(let a=e.begin;a<=e.end;a++)t.add(a)});let n=0;for(;t.has(n)&&n<24;)n++;let o=n;for(;!t.has(o+1)&&o<23;)o++;if(n>=24){C.warning("24小时时段已全部配置完成");return}w.value.push({type:3,price:.5,begin:n,end:o,timeRange:[Q().hour(n).minute(0),Q().hour(o).minute(0)]})},x=t=>{w.value.splice(t,1)},R=(t,n)=>{if(!n||n.length!==2)return;const o=n[0].hour(),e=n[1].hour();if(o>=e){C.error("开始时间必须小于结束时间");return}if(w.value.some((i,I)=>I===t?!1:!(e<i.begin||o>i.end))){C.error("时间段与其他时段冲突，请重新选择");return}w.value[t].begin=o,w.value[t].end=e},z=()=>{const t=N.value[s.value];t&&(t.priceData=w.value.map(n=>({begin:n.begin,end:n.end,price:n.price,type:n.type})),te(()=>{P.value&&P.value.updateChart()}))};ee(w,()=>{z()},{deep:!0});const L=async()=>{const t=new Set;w.value.forEach(o=>{for(let e=o.begin;e<=o.end;e++)t.add(e)});const n=[];for(let o=0;o<24;o++)t.has(o)||n.push(o);if(n.length>0){C.warning(`以下时段未配置: ${n.map(o=>o+":00").join(", ")}`);return}if($.value){const o=N.value[s.value];if(o){const e=w.value.map(i=>({begin:i.begin,end:i.end,price:i.price,type:i.type})),a=g.value.findIndex(i=>i.id===$.value.id);if(a>-1){const i=g.value[a];i.groupedTouPriceByMonth||(i.groupedTouPriceByMonth=[]);const I=i.groupedTouPriceByMonth.findIndex(V=>V.begin===o.begin&&V.end===o.end);if(I>-1?i.groupedTouPriceByMonth[I].groupedTouPriceByDay=e:i.groupedTouPriceByMonth.push({begin:o.begin,end:o.end,groupedTouPriceByDay:e}),g.value[a]={...i},ie(i)){if(!await se(i))return;i.isNew&&(delete i.isNew,g.value[a]={...i}),C.success("电价配置保存成功")}else C.success("当前时段配置保存成功，请继续配置其他时段")}}}te(()=>{P.value&&P.value.updateChart()}),M.value=!1},Y=t=>{const n=[];return w.value.forEach((o,e)=>{if(e!==t)for(let a=o.begin;a<=o.end;a++)n.push(a)}),n};ee(H,t=>{const n=[],o=e=>{e.forEach(a=>{a.isLeaf||n.push(a.key),a.children&&o(a.children)})};o(t),b.value=n,S.value.length===0&&g.value.length>0&&(S.value=[g.value[0].id.toString()],console.log("树形数据更新后默认选中第一个地区:",g.value[0]))},{immediate:!0});const Z=async()=>{try{const{code:t,msg:n,data:o}=await Re({});t===0?(g.value=o.result.map(e=>{const{zone:a,price:i}=e,I=Object.keys(i),q=I.length>0?I[0]:new Date().getFullYear().toString(),V=i[q]||[];return{id:a.id,zoneId:a.id,region:a.region,country:a.country,province:a.province,city:a.city,currentYear:q,period:re(V),createTime:Date.now(),groupedTouPriceByMonth:V.map(G=>({begin:G.begin,end:G.end,groupedTouPriceByDay:G.groupedTouPriceByDay}))}}),console.log("转换后的数据:",g.value),g.value.length>0&&(S.value=[g.value[0].id.toString()],console.log("默认选中第一个地区:",g.value[0]))):C.error(n)}catch(t){console.error("获取电价配置失败:",t),C.error("获取电价配置失败")}},se=async t=>{try{const n=t.currentYear||new Date().getFullYear().toString(),o={zone:{id:t.zoneId,region:t.region,country:t.country,province:t.province,city:t.city},price:{[n]:t.groupedTouPriceByMonth.map(i=>({begin:i.begin,end:i.end,groupedTouPriceByDay:i.groupedTouPriceByDay}))}};console.log("提交的数据:",o);const{code:e,msg:a}=await ze(o);return e===0?(C.success("电价配置更新成功"),!0):(C.error(a||"更新失败"),!1)}catch(n){return console.error("更新电价配置失败:",n),C.error("更新失败"),!1}},re=t=>{if(!t||t.length===0)return"quarter";if(t.length===1){const n=t[0];if(n.begin===1&&n.end===12)return"year"}if(t.length===2){const n=t.some(e=>e.begin===1&&e.end===6),o=t.some(e=>e.begin===7&&e.end===12);if(n&&o)return"halfYear"}return t.length===4&&[{begin:1,end:3},{begin:4,end:6},{begin:7,end:9},{begin:10,end:12}].every(e=>t.some(a=>a.begin===e.begin&&a.end===e.end))?"quarter":t.length===12&&Array.from({length:12},(o,e)=>e+1).every(o=>t.some(e=>e.begin===o&&e.end===o))?"month":"quarter"},ie=t=>{if(!t||!t.period)return!1;const{period:n,groupedTouPriceByMonth:o}=t;let e=[];switch(n){case"year":e=[{begin:1,end:12}];break;case"halfYear":e=[{begin:1,end:6},{begin:7,end:12}];break;case"quarter":e=[{begin:1,end:3},{begin:4,end:6},{begin:7,end:9},{begin:10,end:12}];break;case"month":e=Array.from({length:12},(a,i)=>({begin:i+1,end:i+1}));break;default:return!1}return e.every(a=>{const i=o==null?void 0:o.find(I=>I.begin===a.begin&&I.end===a.end);return i&&i.groupedTouPriceByDay&&i.groupedTouPriceByDay.length>0})};return le(async()=>{await Z(),g.value.length===0&&console.log("没有获取到数据，添加测试数据")}),(t,n)=>{const o=c("a-button"),e=c("a-popconfirm"),a=c("a-tree"),i=c("a-tag"),I=c("a-tab-pane"),q=c("a-tabs"),V=c("a-empty"),G=c("a-select-option"),xe=c("a-select"),ue=c("a-form-item"),ke=c("a-input-number"),we=c("a-time-range-picker"),Ie=c("a-form"),Ce=c("a-card"),$e=c("a-space"),Se=c("a-drawer");return m(),K("div",Oe,[d("div",Ve,[d("div",je,[d("div",We,[Qe,r(o,{type:"primary",size:"small",onClick:n[0]||(n[0]=D=>U.value=!0)},{default:l(()=>[r(j(de)),h(" 新增 ")]),_:1})]),d("div",Je,[r(a,{"tree-data":H.value,"selected-keys":S.value,"expanded-keys":b.value,"default-expand-all":!0,onSelect:B,onExpand:T},{title:l(({title:D,key:O,isLeaf:F})=>[d("div",Xe,[d("span",et,J(D),1),F?(m(),K("div",tt,[r(e,{title:"确定删除此地区配置吗？",onConfirm:Pt=>_(O),onClick:n[1]||(n[1]=Ue(()=>{},["stop"]))},{default:l(()=>[r(o,{size:"small",type:"link",danger:""},{default:l(()=>[h("删除")]),_:1})]),_:2},1032,["onConfirm"])])):W("",!0)])]),_:1},8,["tree-data","selected-keys","expanded-keys"])])]),d("div",nt,[$.value?(m(),K("div",at,[d("div",ot,[d("div",lt,[d("h3",null,[h(J($.value.province)+J($.value.city?"-"+$.value.city:"")+" ",1),$.value.isNew?(m(),E(i,{key:0,color:"orange",style:{"margin-left":"8px"}},{default:l(()=>[h(" 未保存 ")]),_:1})):W("",!0)])])]),d("div",st,[r(q,{activeKey:s.value,"onUpdate:activeKey":n[2]||(n[2]=D=>s.value=D),onChange:u},{default:l(()=>[(m(!0),K(ne,null,ae(N.value,(D,O)=>(m(),E(I,{key:O,tab:D.label},{default:l(()=>[d("div",rt,[d("div",it,[ut,r(o,{type:"primary",size:"small",onClick:f},{default:l(()=>[h(" 编辑 ")]),_:1})]),d("div",ct,[r(Ge,{ref_for:!0,ref_key:"priceChartRef",ref:P,data:D.priceData,onSelect:v},null,8,["data"])])])]),_:2},1032,["tab"]))),128))]),_:1},8,["activeKey"])])])):(m(),K("div",dt,[r(V,{description:"请选择左侧地区查看电价配置"})]))])]),r(Le,{visible:U.value,"onUpdate:visible":n[3]||(n[3]=D=>U.value=D),"existing-regions":g.value,onConfirm:y},null,8,["visible","existing-regions"]),r(Se,{title:"电价时段编辑",visible:M.value,width:500,onClose:n[5]||(n[5]=D=>M.value=!1)},{footer:l(()=>[r($e,null,{default:l(()=>[r(o,{onClick:n[4]||(n[4]=D=>M.value=!1)},{default:l(()=>[h("取消")]),_:1}),r(o,{type:"primary",onClick:L},{default:l(()=>[h("确认")]),_:1})]),_:1})]),default:l(()=>[d("div",pt,[d("div",_t,[(m(!0),K(ne,null,ae(w.value,(D,O)=>(m(),K("div",{key:O,class:"period-item"},[r(Ce,{size:"small"},{title:l(()=>[d("div",ft,[d("span",null,"时段 "+J(O+1),1),r(o,{size:"small",danger:"",type:"text",onClick:F=>x(O)},{default:l(()=>[h(" 删除 ")]),_:2},1032,["onClick"])])]),default:l(()=>[r(Ie,{"label-col":{span:8},"wrapper-col":{span:16}},{default:l(()=>[r(ue,{label:"类型"},{default:l(()=>[r(xe,{value:D.type,"onUpdate:value":F=>D.type=F,onChange:z},{default:l(()=>[r(G,{value:5},{default:l(()=>[h("尖峰")]),_:1}),r(G,{value:4},{default:l(()=>[h("高峰")]),_:1}),r(G,{value:3},{default:l(()=>[h("平时")]),_:1}),r(G,{value:2},{default:l(()=>[h("低谷")]),_:1}),r(G,{value:1},{default:l(()=>[h("深谷")]),_:1})]),_:2},1032,["value","onUpdate:value"])]),_:2},1024),r(ue,{label:"价格"},{default:l(()=>[r(ke,{value:D.price,"onUpdate:value":F=>D.price=F,min:0,step:.01,style:{width:"100%"},"addon-after":"元/kWh",onChange:z},null,8,["value","onUpdate:value"])]),_:2},1024),r(ue,{label:"时间段"},{default:l(()=>[r(we,{value:D.timeRange,"onUpdate:value":F=>D.timeRange=F,format:"HH:00","minute-step":60,"hour-step":1,"disabled-hours":()=>Y(O),onChange:F=>R(O,F)},null,8,["value","onUpdate:value","disabled-hours","onChange"])]),_:2},1024)]),_:2},1024)]),_:2},1024)]))),128))]),r(o,{type:"dashed",block:"",onClick:p,style:{"margin-top":"16px"}},{default:l(()=>[r(j(de)),h(" 新增时段 ")]),_:1})])]),_:1},8,["visible"])])}}},vt=oe(mt,[["__scopeId","data-v-77efc61c"]]),be=A=>(ge("data-v-13c4781d"),A=A(),ye(),A),gt={class:"body_wrap"},yt={class:"p_wrap"},ht=be(()=>d("div",{class:"title_wrap"},null,-1)),bt={class:"content_wrap",id:"content_wrap"},xt={class:"part_wrap"},kt=be(()=>d("div",{class:"p_title"},[d("div",null,"设备配置"),d("div",{class:"btn_wrap"})],-1)),wt={class:"tab_wrap"},It={key:0,class:"t_btn_wrap"},Ct=["onClick"],$t=["onClick"],St={key:0,class:"t_btn_wrap"},qt=["onClick"],Tt=["onClick"],Rt={key:0,class:"t_btn_wrap"},zt=["onClick"],Dt=["onClick"],Mt={__name:"index",setup(A){pe.useModal(),Ke();const U=k(!1),M=k(!1),g=k(),S=k([]),b=k({}),s=k({}),w=k(!1),P=k(3);k([]);const $=k(!1),H=k(ce().filter(f=>f.s_type)),N=f=>[Ye,Ze,function(){return[]},ce,me,ve][f]().filter(x=>x.s_type),B=(f,p)=>{pe.confirm({title:"确认删除?",async onOk(){const{code:x,msg:R}=await Ee({devIdList:[f.id]});x===0?(C.success("删除成功"),v()):C.error(R)}})},T=(f,p)=>{M.value=!0,$.value=!0,H.value=N(p),s.value=f,b.value=_e.cloneDeep(f)},y=f=>{M.value=!0,$.value=!1,H.value=N(f),console.log("form item:",H.value,f)},_=f=>{P.value=f,v()},u=async()=>{var L;await g.value.validateFields();const f=P.value,p={manufacturer:b.value.manufacturer,model:b.value.model};let x,R;w.value=!0;const z=_e.cloneDeep(b.value);if(delete z.manufacturer,delete z.model,delete z.id,delete z.category,$.value){p.id=(L=s.value)==null?void 0:L.id;const Y=await De([{type:f,baseInfo:p,params:z}]);x=Y.code,R=Y.msg}else{const Y=await Me([{type:f,baseInfo:p,params:z}]);x=Y.code,R=Y.msg}w.value=!1,x===0?v():C.error(R),M.value=!1},v=async f=>{U.value=!0;const{code:p,data:x,msg:R}=await Pe({type:P.value});p===0?(S.value=x.result.map(z=>{const{baseInfo:L,params:Y}=z;return{...L,...Y}}),console.log("table:",S.value)):C.error(R),U.value=!1};return le(()=>{v()}),(f,p)=>{const x=c("a-tab-pane"),R=c("a-button"),z=c("a-table"),L=c("a-tag"),Y=c("a-tabs"),Z=c("a-input-number"),se=c("a-input"),re=c("a-select-option"),ie=c("a-select"),t=c("a-form-item"),n=c("a-form"),o=c("a-modal");return m(),K("div",gt,[d("div",yt,[ht,d("div",bt,[d("div",xt,[kt,d("div",wt,[r(Y,{activeKey:P.value,"onUpdate:activeKey":p[3]||(p[3]=e=>P.value=e),onChange:_},{default:l(()=>[(m(),E(x,{key:0,tab:"光伏"},{default:l(()=>[h(" 光伏 ")]),_:1})),(m(),E(x,{key:3,tab:"储能"},{default:l(()=>[r(R,{style:{margin:"-10px 0 10px 0"},size:"small",onClick:p[0]||(p[0]=e=>y(3))},{default:l(()=>[h("新增")]),_:1}),r(z,{size:"small",loading:U.value,pagination:!1,columns:j(ce)(),rowKey:"id","data-source":S.value,defaultExpandAllRows:!0},{bodyCell:l(({column:e,record:a})=>[e.key==="action"?(m(),K("div",It,[d("a",{class:"a_item",onClick:i=>T(a,3)},"修改",8,Ct),d("a",{class:"a_item",onClick:i=>B(a,3)},"删除",8,$t)])):W("",!0)]),_:1},8,["loading","columns","data-source"])]),_:1})),(m(),E(x,{key:4,tab:"电解槽"},{default:l(()=>[r(R,{style:{margin:"-10px 0 10px 0"},size:"small",onClick:p[1]||(p[1]=e=>y(4))},{default:l(()=>[h("新增")]),_:1}),r(z,{size:"small",loading:U.value,pagination:!1,columns:j(me)(),rowKey:"id","data-source":S.value,defaultExpandAllRows:!0},{bodyCell:l(({column:e,record:a,text:i})=>{var I;return[e.key==="action"?(m(),K("div",St,[d("a",{href:"void:0",class:"a_item",onClick:q=>T(a,4)},"修改",8,qt),d("a",{href:"void:0",class:"a_item",onClick:q=>B(a,4)},"删除",8,Tt)])):W("",!0),e.key==="ele_type"?(m(),E(L,{key:1,color:(I=j(fe)(e.options,i))==null?void 0:I.color},{default:l(()=>{var q;return[h(J((q=j(fe)(e.options,i))==null?void 0:q.label),1)]}),_:2},1032,["color"])):W("",!0)]}),_:1},8,["loading","columns","data-source"])]),_:1})),(m(),E(x,{key:5,tab:"储罐"},{default:l(()=>[r(R,{style:{margin:"-10px 0 10px 0"},size:"small",onClick:p[2]||(p[2]=e=>y(5))},{default:l(()=>[h("新增")]),_:1}),r(z,{size:"small",loading:U.value,pagination:!1,columns:j(ve)(),rowKey:"id","data-source":S.value,defaultExpandAllRows:!0},{bodyCell:l(({column:e,record:a})=>[e.key==="action"?(m(),K("div",Rt,[d("a",{href:"void:0",class:"a_item",onClick:i=>T(a,5)},"修改",8,zt),d("a",{href:"void:0",class:"a_item",onClick:i=>B(a,5)},"删除",8,Dt)])):W("",!0)]),_:1},8,["loading","columns","data-source"])]),_:1})),(m(),E(x,{key:6,tab:"电价"},{default:l(()=>[r(vt)]),_:1}))]),_:1},8,["activeKey"])])])])]),r(o,{open:M.value,"onUpdate:open":p[4]||(p[4]=e=>M.value=e),title:$.value?"修改设备":"创建设备",onOk:u,"confirm-loading":w.value,destroyOnClose:""},{default:l(()=>[d("div",null,[r(n,{labelAlign:"left2",ref_key:"formRef",ref:g,model:b.value,name:"basic","label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:l(()=>[(m(!0),K(ne,null,ae(H.value,e=>(m(),E(t,{label:e.title,name:e.dataIndex,rules:e.rules},{default:l(()=>[e.s_type==="number"?(m(),E(Z,{key:0,style:{width:"100%"},value:b.value[e.dataIndex],"onUpdate:value":a=>b.value[e.dataIndex]=a,size:"small",min:0},null,8,["value","onUpdate:value"])):e.s_type==="string"?(m(),E(se,{key:1,value:b.value[e.dataIndex],"onUpdate:value":a=>b.value[e.dataIndex]=a},null,8,["value","onUpdate:value"])):e.s_type==="select"?(m(),E(ie,{key:2,value:b.value[e.dataIndex],"onUpdate:value":a=>b.value[e.dataIndex]=a,style2:"width: 120px"},{default:l(()=>[(m(!0),K(ne,null,ae(e.options,a=>(m(),E(re,{value:a.value},{default:l(()=>[h(J(a.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value"])):W("",!0)]),_:2},1032,["label","name","rules"]))),256))]),_:1},8,["model"])])]),_:1},8,["open","title","confirm-loading"])])}}},Yt=oe(Mt,[["__scopeId","data-v-13c4781d"]]);export{Yt as default};
