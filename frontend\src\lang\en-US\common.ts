export default {
  // Language selection
  language: {
    chinese: '中文',
    english: 'English'
  },
  
  // Common buttons
  button: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    detail: 'Detail',
    create: 'Create',
    search: 'Search',
    reset: 'Reset',
    submit: 'Submit',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    finish: 'Finish',
    close: 'Close',
    view: 'View',
    download: 'Download',
    upload: 'Upload',
    export: 'Export',
    import: 'Import'
  },
  
  // Common status
  status: {
    loading: 'Loading...',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    noData: 'No Data',
    processing: 'Processing...',
    completed: 'Completed',
    failed: 'Failed'
  },
  
  // Common actions
  action: {
    action: 'Action',
    add: 'Add',
    remove: 'Remove',
    update: 'Update',
    refresh: 'Refresh',
    copy: 'Copy',
    paste: 'Paste',
    cut: 'Cut',
    select: 'Select',
    selectAll: 'Select All',
    clear: 'Clear',
    filter: 'Filter',
    sort: 'Sort'
  },
  
  // Form validation
  validation: {
    required: 'This field is required',
    email: 'Please enter a valid email address',
    phone: 'Please enter a valid phone number',
    url: 'Please enter a valid URL',
    number: 'Please enter a valid number',
    integer: 'Please enter an integer',
    positive: 'Please enter a positive number',
    min: 'Minimum value is {min}',
    max: 'Maximum value is {max}',
    minLength: 'Minimum length is {min} characters',
    maxLength: 'Maximum length is {max} characters'
  }
}
