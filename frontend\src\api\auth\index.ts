import { get, post, urlPrefix, type Res } from '../index'

export interface LoginReq {
  username: string;
  password: string;
}

export interface UserInfo {
  id: string;
  userName: string;
  nickName: string;
  role: 1 | 2; // 1-管理员 2-普通用户
}
export const login = async (params: LoginReq): Promise<Res<undefined>> => {
  return await post(`${urlPrefix}/sys/login`, params);
}

export const loginOut = async (): Promise<Res<undefined>> => {
  return await post(`${urlPrefix}/sys/logout`);
}

export const getUserInfo = async (): Promise<Res<UserInfo>> => {
  return await get(`${urlPrefix}/sys/getUser`);
}
