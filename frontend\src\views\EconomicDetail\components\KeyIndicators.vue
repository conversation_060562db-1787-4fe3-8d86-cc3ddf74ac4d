<template>
  <div class="key-indicators">
    <h2 class="section-title">关键指标</h2>
    <div class="indicators-grid">
      <div 
        v-for="indicator in keyIndicatorItems" 
        :key="indicator.dataKey" 
        class="indicator-card"
      >
        <div class="indicator-value">
          {{ indicator.formatter(getDataValue(resultData, indicator.dataKey)) }}{{ indicator.unit }}
        </div>
        <div class="indicator-label">{{ indicator.label }}</div>
      </div>
    </div>

    <!-- 汇总卡片 -->
    <div class="summary-cards">
      <div 
        v-for="card in summaryCardsConfig" 
        :key="card.title" 
        class="summary-card"
      >
        <h3 class="card-title">{{ card.title }}</h3>
        <div class="card-content">
          <div 
            v-for="item in card.items" 
            :key="item.dataKey" 
            class="data-item"
          >
            <span class="label">{{ item.label }}</span>
            <span class="value">{{ item.formatter(getDataValue(resultData, item.dataKey)) }}</span>
            <span class="unit">{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { 
  keyIndicatorItems, 
  summaryCardsConfig, 
  getDataValue 
} from '../util.js'

// 接收父组件传递的数据
defineProps({
  resultData: {
    type: Object,
    default: () => null
  }
})
</script>

<style scoped lang="less">
@import '@/style/base.less';

.key-indicators {
  background: white;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 12px;
  color: #333;
}

.indicators-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.indicator-card {
  padding: 16px;
  border-radius: 4px;
  text-align: center;
  background: @jpDetailCardColor; // @baseColor #E0EBFA
  // background: lighten(@baseColor, 58%); // @baseColor #E0EBFA
}

.indicator-value {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.indicator-label {
  font-size: 12px;
  opacity: 0.9;
}

/* 汇总卡片样式 */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin-top: 20px;
}

.summary-card {
  background: white;
  border-radius: 4px;
  padding: 16px;
  border: 1px solid #e8e8e8;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 12px 0;
  color: #333;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  min-height: 24px;
}

.data-item .label {
  flex: 1;
  color: #666;
  text-align: left;
  padding-right: 8px;
}

.data-item .value {
  min-width: 80px;
  color: #333;
  text-align: right;
  font-weight: 500;
  padding-right: 4px;
}

.data-item .unit {
  min-width: 50px;
  color: #666;
  text-align: left;
  font-size: 11px;
}

/* PDF导出时的样式优化 */
:deep(.pdf-export-mode) .key-indicators {
  margin-bottom: 20px;
  page-break-inside: avoid;
}

:deep(.pdf-export-mode) .section-title {
  background: #f5f5f5;
  padding: 10px;
  margin-bottom: 15px;
  border: 1px solid #e8e8e8;
  font-weight: bold;
}
</style> 