<template>
  <div class="pv-device-editor">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="厂商" name="manufacturer">
        <a-input 
          v-model:value="formData.manufacturer" 
          placeholder="请输入厂商名称" 
          :disabled="disabled"
        />
      </a-form-item>
      
      <a-form-item label="产品型号" name="model">
        <a-input 
          v-model:value="formData.model" 
          placeholder="请输入产品型号（可选）" 
          :disabled="disabled"
        />
      </a-form-item>
      
      <a-form-item label="衰减率配置" name="dampCurve">
        <div class="damp-curve-section">
          <!-- 时间段编辑区域 -->
          <div class="periods-editor">
            <div class="editor-header">
              <span class="section-title">时间段配置</span>
              <a-button
                type="primary"
                size="small"
                @click="addPeriod"
                :disabled="disabled"
              >
                新增时间段
              </a-button>
            </div>

            <!-- 时间段列表 -->
            <div class="periods-list">
              <div
                v-for="(period, index) in periods"
                :key="period.id || index"
                class="period-row"
              >
                <div class="period-label">衰减率</div>
                <div class="period-inputs">
                  <span class="year-label">第</span>
                  <a-input-number
                    v-model:value="period.startYear"
                    :min="1"
                    size="small"
                    style="width: 60px;"
                    :disabled="disabled"
                    @change="handleInputChange"
                  />
                  <span class="year-label">年</span>
                  <span class="separator">至</span>
                  <span class="year-label">第</span>
                  <a-input-number
                    v-model:value="period.endYear"
                    :min="1"
                    size="small"
                    style="width: 60px;"
                    :disabled="disabled"
                    @change="handleInputChange"
                  />
                  <span class="year-label">年</span>
                  <a-input-number
                    v-model:value="period.dampRate"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    size="small"
                    style="width: 80px;"
                    :disabled="disabled"
                    @change="handleInputChange"
                  />
                  <a-button
                    size="small"
                    danger
                    type="text"
                    @click="removePeriod(index)"
                    :disabled="disabled"
                  >
                    删除
                  </a-button>
                </div>

              </div>
            </div>

          </div>
          
          <!-- 衰减率曲线预览 -->
          <div v-if="dampCurveData.length > 0" class="curve-preview">
            <div class="preview-header">
              <span>衰减率曲线</span>
            </div>
            <div class="preview-content">
              <MiniLineChart
                ref="chartRef"
                :key="chartKey"
                :data="dampCurveData"
                :width="400"
                :height="120"
                :show-axis="true"
                :show-tooltip="true"
                title="衰减率曲线"
              />
            </div>
          </div>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import MiniLineChart from '@/components/MiniLineChart/index.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      manufacturer: '',
      model: '',
      dampCurve: []
    })
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref()
const chartRef = ref()
const chartKey = ref(0) // 用于强制重新渲染图表
const isUpdatingFromProps = ref(false) // 防止递归更新的标志

// 表单数据
const formData = ref({
  manufacturer: '',
  model: '',
  dampCurve: []
})

// 衰减率曲线数据
const dampCurveData = ref([])

// 时间段数据
const periods = ref([
  {
    id: Date.now(),
    startYear: 1,
    endYear: 10, // 移除25年限制，使用更灵活的默认值
    dampRate: 0.95,
    error: null,
    startYearError: null,
    endYearError: null,
    dampRateError: null
  }
])

// 验证错误列表
const validationErrors = ref([])



// 表单验证规则
const rules = {
  manufacturer: [
    { required: true, message: '请输入厂商名称', trigger: 'blur' }
  ],
  model: [
    // 产品型号为可选字段
  ],
  dampCurve: [
    { 
      validator: () => {
        if (!dampCurveData.value || dampCurveData.value.length === 0) {
          return Promise.reject('请配置衰减率曲线')
        }
        return Promise.resolve()
      },
      trigger: 'change'
    }
  ]
}

// 清除校验错误
const clearValidationErrors = () => {
  validationErrors.value = []
  periods.value.forEach(period => {
    period.error = null
    period.startYearError = null
    period.endYearError = null
    period.dampRateError = null
  })
}

// 处理输入变化 - 简化，只更新图表数据，不进行实时校验
const handleInputChange = () => {
  // 清除之前的错误状态
  clearValidationErrors()
  updateDampCurveData()
}

// 添加时间段 - 简化逻辑，移除25年限制
const addPeriod = () => {
  const lastPeriod = periods.value[periods.value.length - 1]
  const startYear = lastPeriod ? lastPeriod.endYear + 1 : 1
  const endYear = lastPeriod ? lastPeriod.endYear + 5 : 10 // 移除25年限制，默认增加5年

  periods.value.push({
    id: Date.now(), // 添加唯一ID
    startYear,
    endYear,
    dampRate: 0.95,
    error: null,
    startYearError: null,
    endYearError: null,
    dampRateError: null
  })

  // 清除错误状态并更新图表
  clearValidationErrors()
  updateDampCurveData()
}

// 删除时间段 - 简化逻辑，允许删除任意时间段
const removePeriod = (index) => {
  periods.value.splice(index, 1)

  // 如果删除后没有时间段，添加一个默认时间段
  if (periods.value.length === 0) {
    periods.value.push({
      id: Date.now(),
      startYear: 1,
      endYear: 10, // 移除25年限制，使用更小的默认值
      dampRate: 0.95,
      error: null,
      startYearError: null,
      endYearError: null,
      dampRateError: null
    })
  }

  // 清除错误状态并更新图表
  clearValidationErrors()
  updateDampCurveData()
}

// 验证时间段配置 - 简化为确认时校验，返回错误信息数组
const validatePeriods = () => {
  const errors = []

  // 基本字段验证
  periods.value.forEach((period, index) => {
    // 验证必填字段
    if (!period.startYear) {
      errors.push(`时段${index + 1}: 请输入开始年份`)
    }
    if (!period.endYear) {
      errors.push(`时段${index + 1}: 请输入结束年份`)
    }
    if (period.dampRate === null || period.dampRate === undefined) {
      errors.push(`时段${index + 1}: 请输入衰减率`)
    }

    // 验证数值范围
    if (period.startYear && period.startYear < 1) {
      errors.push(`时段${index + 1}: 开始年份不能小于1`)
    }
    if (period.endYear && period.endYear < 1) {
      errors.push(`时段${index + 1}: 结束年份不能小于1`)
    }
    if (period.dampRate !== null && period.dampRate !== undefined) {
      if (period.dampRate < 0) {
        errors.push(`时段${index + 1}: 衰减率不能小于0`)
      }
      if (period.dampRate > 1) {
        errors.push(`时段${index + 1}: 衰减率不能大于1`)
      }
    }

    // 验证年份范围关系
    if (period.startYear && period.endYear && period.startYear > period.endYear) {
      errors.push(`时段${index + 1}: 开始年份不能大于结束年份`)
    }
  })

  // 如果基本验证通过，检查时间段连续性和重复性
  if (errors.length === 0 && periods.value.length > 0) {
    const sortedPeriods = [...periods.value].sort((a, b) => a.startYear - b.startYear)

    // 检查是否从第1年开始
    if (sortedPeriods[0].startYear !== 1) {
      errors.push('时间段需要从第1年开始')
    }

    // 检查连续性和重复
    for (let i = 0; i < sortedPeriods.length - 1; i++) {
      const current = sortedPeriods[i]
      const next = sortedPeriods[i + 1]

      if (current.endYear + 1 !== next.startYear) {
        if (current.endYear >= next.startYear) {
          errors.push(`时段${i + 1}(第${current.startYear}-${current.endYear}年)和时段${i + 2}(第${next.startYear}-${next.endYear}年)时间重叠`)
        } else {
          const gapStart = current.endYear + 1
          const gapEnd = next.startYear - 1
          errors.push(`时段${i + 1}和时段${i + 2}之间存在间隔(第${gapStart}-${gapEnd}年未覆盖)，时间段需要连续`)
        }
      }
    }

    // 检查是否有重复的年份范围
    const maxYear = Math.max(...periods.value.map(p => p.endYear || 0))
    if (maxYear > 0) {
      const yearCoverage = new Array(maxYear).fill(0)
      periods.value.forEach((period) => {
        if (period.startYear && period.endYear) {
          for (let year = period.startYear; year <= period.endYear; year++) {
            if (year >= 1 && year <= maxYear) {
              yearCoverage[year - 1]++
              if (yearCoverage[year - 1] > 1) {
                errors.push(`第${year}年被多个时间段重复包含`)
              }
            }
          }
        }
      })
    }
  }

  return errors
}

// 更新衰减率曲线数据 - 简化逻辑，始终更新图表
const updateDampCurveData = () => {
  // 动态计算最大年份，移除25年限制
  const maxYear = Math.max(...periods.value.map(p => p.endYear || 0), 10) // 至少10年作为默认
  const data = new Array(maxYear).fill(0)

  periods.value.forEach(period => {
    if (period.startYear && period.endYear && period.dampRate !== null && period.dampRate !== undefined) {
      for (let year = period.startYear; year <= period.endYear; year++) {
        if (year <= maxYear && year >= 1) {
          data[year - 1] = period.dampRate
        }
      }
    }
  })

  dampCurveData.value = [...data]
  formData.value.dampCurve = [...data]

  // 强制重新渲染图表
  chartKey.value++

  // 触发数据变化事件
  if (!isUpdatingFromProps.value) {
    emitChange()
  }
}

// 从衰减率数据初始化时间段
const initializePeriodsFromData = (data) => {
  if (!data || data.length === 0) {
    periods.value = [{
      id: Date.now(),
      startYear: 1,
      endYear: 10, // 移除25年限制
      dampRate: 0.95,
      error: null,
      startYearError: null,
      endYearError: null,
      dampRateError: null
    }]
    return
  }

  // 从数组数据反推时间段配置
  const newPeriods = []
  let currentValue = data[0]
  let startYear = 1

  for (let i = 1; i <= data.length; i++) {
    if (i === data.length || data[i] !== currentValue) {
      newPeriods.push({
        id: Date.now() + Math.random(), // 添加唯一ID
        startYear,
        endYear: i,
        dampRate: currentValue,
        error: null,
        startYearError: null,
        endYearError: null,
        dampRateError: null
      })

      if (i < data.length) {
        currentValue = data[i]
        startYear = i + 1
      }
    }
  }

  if (newPeriods.length > 0) {
    periods.value = newPeriods
  }
}

// 发送数据变化事件
const emitChange = () => {
  // 如果正在从props更新，则不触发emit，避免递归
  if (isUpdatingFromProps.value) {
    return
  }

  const data = {
    manufacturer: formData.value.manufacturer,
    model: formData.value.model,
    dampCurve: dampCurveData.value
  }
  emit('update:modelValue', data)
  emit('change', data)
}

// 监听表单数据变化
watch(() => formData.value.manufacturer, emitChange)
watch(() => formData.value.model, emitChange)

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    // 设置标志，防止递归更新
    isUpdatingFromProps.value = true

    formData.value.manufacturer = newValue.manufacturer || ''
    formData.value.model = newValue.model || ''

    if (newValue.dampCurve && newValue.dampCurve.length > 0) {
      dampCurveData.value = [...newValue.dampCurve]
      formData.value.dampCurve = [...newValue.dampCurve]
      initializePeriodsFromData(newValue.dampCurve)
    } else {
      dampCurveData.value = []
      formData.value.dampCurve = []
      initializePeriodsFromData([])
    }

    // 重置标志
    nextTick(() => {
      isUpdatingFromProps.value = false
    })
  }
}, { immediate: true, deep: true })

// 验证表单 - 在确认时进行完整校验
const validate = async () => {
  try {
    // 先验证基本表单字段
    await formRef.value.validateFields()

    // 验证时间段配置
    const periodErrors = validatePeriods()
    if (periodErrors.length > 0) {
      // 显示时间段校验错误
      message.error({
        content: `时间段配置错误：\n${periodErrors.join('\n')}`,
        duration: 5
      })
      return false
    }

    return true
  } catch (error) {
    console.log('表单验证失败:', error)
    return false
  }
}

// 重置表单
const resetFields = () => {
  formData.value = {
    manufacturer: '',
    model: '',
    dampCurve: []
  }
  dampCurveData.value = []
  periods.value = [{
    id: Date.now(),
    startYear: 1,
    endYear: 10, // 移除25年限制，使用更灵活的默认值
    dampRate: 0.95,
    error: null,
    startYearError: null,
    endYearError: null,
    dampRateError: null
  }]
  formRef.value?.resetFields()
}

// 获取表单数据
const getFormData = () => {
  return {
    type: 1, // 光伏设备类型
    baseInfo: {
      manufacturer: formData.value.manufacturer,
      model: formData.value.model
    },
    params: {
      damp_curve: {
        data: dampCurveData.value
      }
    }
  }
}

// 初始化图表数据
updateDampCurveData()

// 暴露方法
defineExpose({
  validate,
  resetFields,
  getFormData
})
</script>

<style scoped>
.pv-device-editor {
  padding: 0;
}

.damp-curve-section {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.curve-preview {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  background: white;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
}

.preview-content {
  text-align: center;
}

/* 内联编辑样式 */
.periods-editor {
  margin-bottom: 16px;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-weight: 500;
  color: #333;
}

.periods-list {
  margin-bottom: 12px;
}

.period-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background: #fafafa;
}

.period-label {
  width: 60px;
  line-height: 24px;
  font-size: 12px;
  color: #666;
  flex-shrink: 0;
}

.period-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.year-label {
  font-size: 12px;
  color: #666;
  line-height: 24px;
}

.separator {
  font-size: 12px;
  color: #666;
  margin: 0 4px;
}


</style>
