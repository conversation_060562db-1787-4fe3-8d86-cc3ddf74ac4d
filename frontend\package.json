{"name": "test1", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 8891", "dev.prod": "vite  --mode prod", "dev.test": "vite  --mode test", "dev.zh": "vite  --mode zh", "build": "run-p type-check \"build-only {@}\" --", "build.prod": "run-p type-check \"build-only:prod {@}\" --", "build.test": "run-p type-check \"build-only:test {@}\" --", "build.zh": "run-p type-check \"build-only:zh {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "start-server-and-test preview http://localhost:4173 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "build-only": "vite build", "build-only.prod": "vite build --mode prod", "build-only.test": "vite build --mode test", "build-only.zh": "vite build --mode zh", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.1", "axios": "^1.6.8", "dayjs": "^1.11.10", "decimal.js": "^10.5.0", "docx": "^9.5.1", "echarts": "^5.5.1", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "lodash": "^4.17.21", "normalize.css": "^8.0.1", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-i18n": "^9.13.1", "vue-router": "^4.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/jsdom": "^21.1.6", "@types/node": "^20.12.5", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/test-utils": "^2.4.5", "@vue/tsconfig": "^0.5.1", "body-parser": "^1.20.2", "cypress": "^13.7.2", "eslint": "^8.57.0", "eslint-plugin-cypress": "^2.15.1", "eslint-plugin-vue": "^9.23.0", "express": "^4.19.2", "http-proxy-middleware": "^3.0.0", "jsdom": "^24.0.0", "less": "^4.2.0", "less-loader": "^12.2.0", "npm-run-all2": "^6.1.2", "prettier": "^3.2.5", "start-server-and-test": "^2.0.3", "typescript": "~5.4.0", "vite": "^5.2.8", "vite-plugin-vue-devtools": "^7.0.25", "vitest": "^1.4.0", "vue-tsc": "^2.0.11"}}