// import { AxiosPromise} from 'axios'
import { get, post, urlPrefix, type Res, del } from '../index'

export interface TaskItem {
  [key: string]: any // TODO
}

export interface PolicyItem {
  id: number;
  name: string;
  status: number; // 0 未启用；1 启用
  desc: string;
  // 以下暂时自定义
  score: number;
  creator: string; // 定制人
  period: string; // 定制周期
}

export interface PlanItem {
  id: number;
  dev_id: number; // 1-6. 1-4 ALK, 5-PV, 6-SOC, 8-Load
  prop_id: number;
  type: number;
  interval: number;
  data: number[];
  begin_time: string;
  end_time: string;
  create_time: string;
  desc: string;
}

// export const submitTask = async (params: TaskItem): Promise<Res<any>> => {
//   return await post(`${urlPrefix}/gsct/submitTask`, params, {
//     'Content-Type' : 'multipart/form-data'
//   });
// }

export const createPolicy = async (params: PolicyItem): Promise<Res<any>> => {
  return await post(`${urlPrefix}/policyManager/policy`, params);
}

export const getPolicy = async (params: PolicyItem): Promise<Res<PolicyItem[]>> => {
  return await get(`${urlPrefix}/policyManager/policy`, params);
}

export const deletePolicy = async (id: number ): Promise<Res<any>> => {
  return await del(`${urlPrefix}/policyManager/policy/${id}/delete`);
}

export const getPlan = async (): Promise<Res<PlanItem[]>> => {
  return await get(`${urlPrefix}/operationManager/plan`);
}

export const getReal = async (): Promise<Res<PlanItem[]>> => {
  return await get(`${urlPrefix}/operationManager/measure`);
}
