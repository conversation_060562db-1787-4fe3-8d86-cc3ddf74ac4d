


export const createALKOptions = (params) => {
  const pieces = []
  // const statusColor = ['#5cc', '#c5c']
  const statusColor = ['#888', '#87d068']

  for (let i = 0; i < 24; i++) {
    pieces.push({ gt: i, lt: i + 1, color: statusColor[params.plan[i] === 0 ? 0 : 1]})
  }
  
  return {
    title: {
      text: params.title,
      textStyle: {
        color: '#55c'
      }
    },
    color: ['#5cc', '#1677ff'], // legend 和 line 的颜色对应
    tooltip: {
      trigger: 'axis',
      formatter: function(params) { // 自定义 tooltip
        // console.log('p:', params)
        const html = `
          <div style="display:flex;align-items:center;"><div style="width:10px;height:10px;background:#1677ff;margin-right:5px;border-radius:50%"></div>${params[1].seriesName} ${params[1].value}</div>
          <div style="display:flex;align-items:center;"><div style="width:10px;height:10px;background:#5cc;margin-right:5px;border-radius:50%"></div>${params[2].seriesName} ${params[2].value || '-'}</div>
        `
        return html
      }
    },

    legend: {
      data: ['下发', '实时'],
      top: '5%',
      right: '0',
      icon: 'circle',
      itemHeight: 10, // icon 大小
    },
    grid: {
      top: '20%', // 调整顶部留白
      bottom: '10%', // 调整底部留白
      left: '5%',
      right: '5%',
      containLabel: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      name: 'MW'
      // max: 550 // y轴最大值
    },
    visualMap: {
      type: 'piecewise',
      show: false,
      dimension: 0,
      seriesIndex: 0,
      pieces: pieces
    },
    series: [
      {
        type: 'line',
        smooth: 0.6,
        symbol: 'none',
        lineStyle: {
          // color: '#5470C6',
          width: 0
        },
        markLine: {
          // symbol: ['none', 'none'],
          // label: { show: false },
          // data: [{ xAxis: 1 }, { xAxis: 3 }, { xAxis: 5 }, { xAxis: 7 }]
        },
        areaStyle: {},
        data: new Array(24).fill(20)
      },
      {
        name: '下发',
        type: 'line',
        // stack: 'Total',
        data: params.plan,
        symbolSize:0,// 圆圈尺寸
        lineStyle: {
          // showSymbol: false,
          symbolSize: 3,
          color: '#1677ff',
          // width: 2
        }
      },
      {
        name: '实时',
        type: 'line',
        // stack: 'Total',
        data: params.real,
        symbolSize:0,// 圆圈尺寸
        lineStyle: {
          // showSymbol: false,
          symbolSize: 3,
          color: '#5cc',
          // width: 2
        }
      },
    ],
  }
}



export const createLoadOptions = (params) => {
  return {
    title: {
      text: params.title
    },
    grid: {
      top: '15%', // 调整顶部留白
      bottom: '5%', // 调整底部留白
      left: '5%',
      right: '5%',
      containLabel: true,
   
    },
    legend: {
      data: ['原始预测', '预测修正', '实时'],
      top: '0%',
      right: '0',
      icon: 'circle', // 圆圈 icon
      itemHeight: 10, // icon 大小
    },
    // xAxis: {
    //   // boundaryGap: false,
    // },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '20%'],
      // boundaryGap: false,
      splitLine: {
        show: false // 去掉平行于 x 轴的刻度辅助线
      }
    },
    series: [
      {
        name: '原始预测',
        type: 'line',
        stack: 'Total',
        data: params.plan,
        symbolSize:0,// 圆圈尺寸
        lineStyle: {
          type: 'dashed', // 设置为虚线
          // showSymbol: false,
          symbolSize: 3,
          color: '#1677ff',
          width: 3
        }
      },
      {
        name: '预测修正',
        type: 'line',
        stack: 'Total',
        data: params.fix,
        symbolSize:0,// 圆圈尺寸
        lineStyle: {
          type: 'dashed', // 设置为虚线
          showSymbol: false
        }
      },
      {
        name: '实时',
        type: 'line',
        stack: 'Total',
        data: params.real,
        symbolSize:0,// 圆圈尺寸
        lineStyle: {
          type: 'dashed', // 设置为虚线
          showSymbol: false
        }
        // lineStyle: {
        //   color: '#5470C6',
        // },
      },
    ],
  }
}

export const createSingleLineOptions = (params) => {
  return {
    title: {
      text: params.title,
      textStyle: {
        color: '#55c'
      }
    },
    // grid: {
    //   top: '15%', // 调整顶部留白
    //   bottom: '5%', // 调整底部留白
    //   left: '5%',
    //   right: '5%',
    //   containLabel: true,
   
    // },
    // legend: {
    //   data: ['原始预测', '预测修正', '实时'],
    //   top: '0%',
    //   right: '0',
    //   icon: 'circle', // 圆圈 icon
    //   itemHeight: 10, // icon 大小
    // },
    // xAxis: {
    //   // boundaryGap: false,
    // },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '20%'],
      name: 'MW'
      // boundaryGap: false,
      // splitLine: {
      //   show: false // 去掉平行于 x 轴的刻度辅助线
      // }
    },
    // color: ['#5470C6'],
    series: [
      {
        // name: '实时',
        type: 'line',
        // stack: 'Total',
        data: params.plan,
        symbolSize:0,// 圆圈尺寸
        lineStyle: {
          color: '#5470C6',
        },
      },
    ],
  }
}
