<template>
  <div class="pv-device-editor">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="厂商" name="manufacturer">
        <a-input 
          v-model:value="formData.manufacturer" 
          placeholder="请输入厂商名称" 
          :disabled="disabled"
        />
      </a-form-item>
      
      <a-form-item label="产品型号" name="model">
        <a-input 
          v-model:value="formData.model" 
          placeholder="请输入产品型号（可选）" 
          :disabled="disabled"
        />
      </a-form-item>
      
      <a-form-item label="衰减率配置" name="dampCurve">
        <div class="damp-curve-section">
          <!-- 时间段编辑区域 -->
          <div class="periods-editor">
            <div class="editor-header">
              <span class="section-title">时间段配置</span>
              <a-button
                type="primary"
                size="small"
                @click="addPeriod"
                :disabled="!canAddPeriod || disabled"
              >
                新增时间段
              </a-button>
            </div>

            <!-- 时间段列表 -->
            <div class="periods-list">
              <div
                v-for="(period, index) in periods"
                :key="index"
                class="period-row"
              >
                <div class="period-label">衰减率</div>
                <div class="period-inputs">
                  <span class="year-label">第</span>
                  <a-input-number
                    v-model:value="period.startYear"
                    :min="1"
                    size="small"
                    style="width: 60px;"
                    :disabled="disabled"
                    @change="handleInputChange"
                  />
                  <span class="year-label">年</span>
                  <span class="separator">至</span>
                  <span class="year-label">第</span>
                  <a-input-number
                    v-model:value="period.endYear"
                    :min="1"
                    size="small"
                    style="width: 60px;"
                    :disabled="disabled"
                    @change="handleInputChange"
                  />
                  <span class="year-label">年</span>
                  <a-input-number
                    v-model:value="period.dampRate"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    size="small"
                    style="width: 80px;"
                    :disabled="disabled"
                    @change="handleInputChange"
                  />
                  <a-button 
                    size="small" 
                    danger 
                    type="text"
                    @click="removePeriod(index)"
                    :disabled="periods.length <= 1 || disabled"
                  >
                    删除
                  </a-button>
                </div>
                
                <!-- 错误提示 -->
                <div v-if="period.error || period.startYearError || period.endYearError || period.dampRateError" class="period-errors">
                  <div v-if="period.startYearError" class="error-text">{{ period.startYearError }}</div>
                  <div v-if="period.endYearError" class="error-text">{{ period.endYearError }}</div>
                  <div v-if="period.dampRateError" class="error-text">{{ period.dampRateError }}</div>
                  <div v-if="period.error" class="error-text">{{ period.error }}</div>
                </div>
              </div>
            </div>
            
            <!-- 整体校验错误提示 -->
            <div class="validation-summary" v-if="validationErrors.length > 0">
              <a-alert
                type="error"
                :message="`时间段配置错误 (${validationErrors.length}个)`"
                show-icon
                size="small"
              >
                <template #description>
                  <ul class="error-list">
                    <li v-for="(error, index) in validationErrors" :key="index">
                      {{ error }}
                    </li>
                  </ul>
                </template>
              </a-alert>
            </div>
          </div>
          
          <!-- 衰减率曲线预览 -->
          <div v-if="dampCurveData.length > 0" class="curve-preview">
            <div class="preview-header">
              <span>衰减率曲线</span>
            </div>
            <div class="preview-content">
              <MiniLineChart
                ref="chartRef"
                :key="chartKey"
                :data="dampCurveData"
                :width="400"
                :height="120"
                :show-axis="true"
                :show-tooltip="true"
                title="衰减率曲线"
              />
            </div>
          </div>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import MiniLineChart from '@/components/MiniLineChart/index.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      manufacturer: '',
      model: '',
      dampCurve: []
    })
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref()
const chartRef = ref()
const chartKey = ref(0) // 用于强制重新渲染图表
const isUpdatingFromProps = ref(false) // 防止递归更新的标志

// 表单数据
const formData = ref({
  manufacturer: '',
  model: '',
  dampCurve: []
})

// 衰减率曲线数据
const dampCurveData = ref([])

// 时间段数据
const periods = ref([
  {
    startYear: 1,
    endYear: 25,
    dampRate: 0.95,
    error: null,
    startYearError: null,
    endYearError: null,
    dampRateError: null
  }
])

// 验证错误列表
const validationErrors = ref([])

// 是否可以添加新时间段
const canAddPeriod = computed(() => {
  // 移除25年限制，允许添加更多时间段
  return true
})

// 表单验证规则
const rules = {
  manufacturer: [
    { required: true, message: '请输入厂商名称', trigger: 'blur' }
  ],
  model: [
    // 产品型号为可选字段
  ],
  dampCurve: [
    { 
      validator: () => {
        if (!dampCurveData.value || dampCurveData.value.length === 0) {
          return Promise.reject('请配置衰减率曲线')
        }
        return Promise.resolve()
      },
      trigger: 'change'
    }
  ]
}

// 处理输入变化
const handleInputChange = () => {
  validatePeriods()
  updateDampCurveData()
}

// 添加时间段
const addPeriod = () => {
  if (!canAddPeriod.value) return
  
  const lastPeriod = periods.value[periods.value.length - 1]
  const startYear = lastPeriod ? lastPeriod.endYear + 1 : 1
  
  periods.value.push({
    startYear,
    endYear: 25,
    dampRate: 0.95,
    error: null,
    startYearError: null,
    endYearError: null,
    dampRateError: null
  })
  
  validatePeriods()
  updateDampCurveData()
}

// 删除时间段
const removePeriod = (index) => {
  if (periods.value.length <= 1) {
    message.warning('至少需要保留一个时间段')
    return
  }
  
  periods.value.splice(index, 1)
  validatePeriods()
  updateDampCurveData()
}

// 验证时间段
const validatePeriods = () => {
  validationErrors.value = []

  // 清除之前的错误
  periods.value.forEach(period => {
    period.error = null
    period.startYearError = null
    period.endYearError = null
    period.dampRateError = null
  })

  // 检查每个时间段的有效性
  periods.value.forEach((period, index) => {
    let hasError = false

    // 验证开始年份
    if (!period.startYear) {
      period.startYearError = '请输入开始年份'
      hasError = true
    } else if (period.startYear < 1) {
      period.startYearError = '开始年份不能小于1'
      hasError = true
    }

    // 验证结束年份
    if (!period.endYear) {
      period.endYearError = '请输入结束年份'
      hasError = true
    } else if (period.endYear < 1) {
      period.endYearError = '结束年份不能小于1'
      hasError = true
    }

    // 验证衰减率
    if (period.dampRate === null || period.dampRate === undefined) {
      period.dampRateError = '请输入衰减率'
      hasError = true
    } else if (period.dampRate < 0) {
      period.dampRateError = '衰减率不能小于0'
      hasError = true
    } else if (period.dampRate > 1) {
      period.dampRateError = '衰减率不能大于1'
      hasError = true
    }

    // 验证年份范围关系
    if (period.startYear && period.endYear && period.startYear > period.endYear) {
      period.endYearError = '结束年份不能小于开始年份'
      hasError = true
    }

    if (hasError) {
      validationErrors.value.push(`时段${index + 1}存在错误`)
    }
  })

  // 检查时间段是否连续、完整、无重复（仅在单个时间段验证通过后进行）
  const hasIndividualErrors = periods.value.some(period =>
    period.startYearError || period.endYearError || period.dampRateError
  )

  if (!hasIndividualErrors && periods.value.length > 0) {
    const sortedPeriods = [...periods.value].sort((a, b) => a.startYear - b.startYear)

    // 检查是否从第1年开始
    if (sortedPeriods[0].startYear !== 1) {
      validationErrors.value.push('⚠️ 时间段必须从第1年开始')
      sortedPeriods[0].error = '此时间段应从第1年开始'
    }

    // 移除25年结束的强制要求，允许用户自定义结束年份

    // 检查连续性和重复
    for (let i = 0; i < sortedPeriods.length - 1; i++) {
      const current = sortedPeriods[i]
      const next = sortedPeriods[i + 1]

      if (current.endYear + 1 !== next.startYear) {
        if (current.endYear >= next.startYear) {
          validationErrors.value.push(`❌ 时段${i + 1}(第${current.startYear}-${current.endYear}年)和时段${i + 2}(第${next.startYear}-${next.endYear}年)存在重叠`)
          current.error = '与下一时间段重叠'
          next.error = '与上一时间段重叠'
        } else {
          const gapStart = current.endYear + 1
          const gapEnd = next.startYear - 1
          validationErrors.value.push(`⚠️ 时段${i + 1}和时段${i + 2}之间存在间隔(第${gapStart}-${gapEnd}年未覆盖)`)
          current.error = '与下一时间段存在间隔'
          next.error = '与上一时间段存在间隔'
        }
      }
    }

    // 检查是否有重复的年份范围
    // 动态计算最大年份，不再限制为25年
    const maxYear = Math.max(...periods.value.map(p => p.endYear || 0))
    if (maxYear > 0) {
      const yearCoverage = new Array(maxYear).fill(0)
      periods.value.forEach((period) => {
        if (period.startYear && period.endYear) {
          for (let year = period.startYear; year <= period.endYear; year++) {
            if (year >= 1 && year <= maxYear) {
              yearCoverage[year - 1]++
              if (yearCoverage[year - 1] > 1) {
                validationErrors.value.push(`❌ 第${year}年被多个时间段覆盖`)
                period.error = '存在年份重复覆盖'
              }
            }
          }
        }
      })
    }
  }
}

// 更新衰减率曲线数据
const updateDampCurveData = () => {
  if (validationErrors.value.length > 0) {
    dampCurveData.value = []
    formData.value.dampCurve = []
    return
  }

  // 动态计算最大年份，不再限制为25年
  const maxYear = Math.max(...periods.value.map(p => p.endYear || 0), 25) // 至少25年作为默认
  const data = new Array(maxYear).fill(0)

  periods.value.forEach(period => {
    if (period.startYear && period.endYear && period.dampRate !== null) {
      for (let year = period.startYear; year <= period.endYear; year++) {
        if (year <= maxYear && year >= 1) {
          data[year - 1] = period.dampRate
        }
      }
    }
  })

  dampCurveData.value = [...data]
  formData.value.dampCurve = [...data]

  // 强制重新渲染图表
  chartKey.value++

  // 触发数据变化事件
  if (!isUpdatingFromProps.value) {
    emitChange()
  }
}

// 从衰减率数据初始化时间段
const initializePeriodsFromData = (data) => {
  if (!data || data.length === 0) {
    periods.value = [{
      startYear: 1,
      endYear: 25,
      dampRate: 0.95,
      error: null,
      startYearError: null,
      endYearError: null,
      dampRateError: null
    }]
    return
  }

  // 从数组数据反推时间段配置
  const newPeriods = []
  let currentValue = data[0]
  let startYear = 1

  for (let i = 1; i <= data.length; i++) {
    if (i === data.length || data[i] !== currentValue) {
      newPeriods.push({
        startYear,
        endYear: i,
        dampRate: currentValue,
        error: null,
        startYearError: null,
        endYearError: null,
        dampRateError: null
      })

      if (i < data.length) {
        currentValue = data[i]
        startYear = i + 1
      }
    }
  }

  if (newPeriods.length > 0) {
    periods.value = newPeriods
  }
}

// 发送数据变化事件
const emitChange = () => {
  // 如果正在从props更新，则不触发emit，避免递归
  if (isUpdatingFromProps.value) {
    return
  }

  const data = {
    manufacturer: formData.value.manufacturer,
    model: formData.value.model,
    dampCurve: dampCurveData.value
  }
  emit('update:modelValue', data)
  emit('change', data)
}

// 监听表单数据变化
watch(() => formData.value.manufacturer, emitChange)
watch(() => formData.value.model, emitChange)

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    // 设置标志，防止递归更新
    isUpdatingFromProps.value = true

    formData.value.manufacturer = newValue.manufacturer || ''
    formData.value.model = newValue.model || ''

    if (newValue.dampCurve && newValue.dampCurve.length > 0) {
      dampCurveData.value = [...newValue.dampCurve]
      formData.value.dampCurve = [...newValue.dampCurve]
      initializePeriodsFromData(newValue.dampCurve)
    } else {
      dampCurveData.value = []
      formData.value.dampCurve = []
      initializePeriodsFromData([])
    }

    // 重置标志
    nextTick(() => {
      isUpdatingFromProps.value = false
    })
  }
}, { immediate: true, deep: true })

// 验证表单
const validate = async () => {
  try {
    await formRef.value.validateFields()
    return true
  } catch (error) {
    console.log('表单验证失败:', error)
    return false
  }
}

// 重置表单
const resetFields = () => {
  formData.value = {
    manufacturer: '',
    model: '',
    dampCurve: []
  }
  dampCurveData.value = []
  periods.value = [{
    startYear: 1,
    endYear: 25,
    dampRate: 0.95,
    error: null,
    startYearError: null,
    endYearError: null,
    dampRateError: null
  }]
  formRef.value?.resetFields()
}

// 获取表单数据
const getFormData = () => {
  return {
    type: 1, // 光伏设备类型
    baseInfo: {
      manufacturer: formData.value.manufacturer,
      model: formData.value.model
    },
    params: {
      damp_curve: {
        data: dampCurveData.value
      }
    }
  }
}

// 初始验证
validatePeriods()
updateDampCurveData()

// 暴露方法
defineExpose({
  validate,
  resetFields,
  getFormData
})
</script>

<style scoped>
.pv-device-editor {
  padding: 0;
}

.damp-curve-section {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.curve-preview {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  background: white;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
}

.preview-content {
  text-align: center;
}

/* 内联编辑样式 */
.periods-editor {
  margin-bottom: 16px;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-weight: 500;
  color: #333;
}

.periods-list {
  margin-bottom: 12px;
}

.period-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background: #fafafa;
}

.period-label {
  width: 60px;
  line-height: 24px;
  font-size: 12px;
  color: #666;
  flex-shrink: 0;
}

.period-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.year-label {
  font-size: 12px;
  color: #666;
  line-height: 24px;
}

.separator {
  font-size: 12px;
  color: #666;
  margin: 0 4px;
}

.period-errors {
  margin-top: 4px;
  width: 100%;
}

.error-text {
  color: #ff4d4f;
  font-size: 11px;
  line-height: 1.2;
  margin-bottom: 2px;
}

.validation-summary {
  margin-bottom: 12px;
}

.error-list {
  margin: 0;
  padding-left: 16px;
}

.error-list li {
  margin-bottom: 4px;
  color: #ff4d4f;
  font-size: 12px;
}
</style>
