<template>
  <a-layout>
    <a-layout-header style="padding: 0;height: auto;line-height: normal;">
      <Header />
    </a-layout-header>
    <a-layout>
      <a-layout-content style="margin: 0 16px;height: calc(100vh - 64px);overflow: auto;">
        <RouterView />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import MenuItems from '@/components/Menu/menuConfig'
import { RouterView } from 'vue-router';
import Header from '@/components/Header/index.vue'

const collapsed = ref<boolean>(false);
</script>
<style scoped>
#components-layout-demo-side .logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
}

.site-layout .site-layout-background {
  background: #fff;
}
/* [data-theme='dark'] .site-layout .site-layout-background {
  background: #141414;
} */
</style>
