// 电网配置
export const gridConfig = [
  // {
  //   label: '发电制氢比例',
  //   name: 'powerToHydrogenRatio',
  //   unit: '%',
  //   type: 'number',
  //   rules: [{ required: true, message: '请输入' }],
  //   numberType: 'ratio',
  //   visible: true,
  //   notShowFromCapacity: true
  // },
    {
    label: '绿电制氢用电占比',
    name: 'greenPowerH2Ratio',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true,
  },
    {
    label: '绿电其他负荷用电占比',
    name: 'greenPowerPublicLoadRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true,
  },
    {
    label: '绿电上网占比',
    name: 'greenPowerUpGridRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true,
  },
    {
    label: '绿电弃电占比',
    name: 'greenPowerDeprecatedRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true,
  },
  //  {
  //   label: '首年下网电量',
  //   name: 'firstYearDownGridPower',
  //   unit: '万kwh',
  //   type: 'number',
  //   rules: [{ required: true, message: '请输入' }],
  //   visible: true
  // },
  {
    label: '下网制氢用电量',
    name: 'firstYearDownGridH2Power',
    unit: '万kwh',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '下网公共负荷用电量',
    name: 'firstYearDownGridPublicLoadPower',
    unit: '万kwh',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  // {
  //   label: '上网电价含增值税',
  //   name: 'gridElectricityPriceWithTax',
  //   unit: '元/kwh',
  //   type: 'number',
  //   rules: [{ required: true, message: '请输入' }],
  //   visible: true
  // },
  {
    label: '上网不含税电价',
    name: 'gridElectricityPriceNoTax',
    unit: '元/kwh',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  // {
  //   label: '自备电厂含税电价',
  //   name: 'selfPowerPlantElectricityPriceWithTax',
  //   unit: '元/kwh',
  //   type: 'number',
  //   rules: [{ required: true, message: '请输入' }],
  //   visible: true
  // },
  {
    label: '自发绿电不含税电价',
    name: 'selfPowerPlantElectricityPriceNoTax',
    unit: '元/kwh',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '下网不含税电价',
    name: 'gridHydrogenElectricityPriceNoTax',
    unit: '元/kwh',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
] 