import{_ as w,r as p,e as T,f as y,b as C,c as k,g as f,h as t,w as r,i as D,X as j,n as K,F as A,l as E,v as x,E as P,G as F,d as $,y as R,o as B}from"./index-C98MXVsn.js";import{b as Y,a as q}from"./index-CvDCL3_t.js";import{g as G,t as N,a as O,y as V,b as z,c as H,P as L,K as U,S as M}from"./SensitivityAnalysis-CkNrhIec.js";import{e as X,s as J,d as Q}from"./index-BEuFRmea.js";import"./xlsx-D5JNrnKm.js";const W=m=>(P("data-v-5b2fb2ef"),m=m(),F(),m),Z={class:"detail-indicators"},aa={class:"section-header"},ea=W(()=>f("h2",{class:"section-title"},"详情指标",-1)),ta={__name:"TabTables",props:{resultData:{type:Object,default:()=>null}},setup(m){const i=m,n=p("2"),d=p(!1),v=T(()=>{var e,s;return((s=(e=i.resultData)==null?void 0:e.financialIndicatorsSummary)==null?void 0:s.operatingYears)||25}),b=T(()=>v.value+1),c=[{key:"2",tab:"固定资产投资估算",type:"basic",dataKey:"fixedAssetsInvestmentEstimation",configKey:"fixedAssets"},{key:"3",tab:"工程总概算",type:"basic",dataKey:"projectOverallBudget",configKey:"projectBudget"},{key:"4",tab:"融资计划",type:"basic",dataKey:"financingPlan",configKey:"financing"},{key:"5",tab:"投资计划与资金筹措",type:"basic",dataKey:"investmentPlanAndFundRaising",configKey:"investmentPlan"},{key:"6",tab:"年度制氢及上网电量",type:"yearly",dataKey:"annualHydrogenAndGridPower",configKey:"annualHydrogenAndGridPower"},{key:"7",tab:"还本付息计算",type:"yearly",dataKey:"loanRepaymentSchedule",configKey:"loanRepaymentSchedule"},{key:"8",tab:"总成本费用",type:"yearly",dataKey:"totalCostAndExpenses",configKey:"totalCostAndExpenses"},{key:"9",tab:"利润和利润分配",type:"yearly",dataKey:"profitAndProfitDistribution",configKey:"profitAndProfitDistribution"},{key:"10",tab:"项目投资现金流量",type:"yearly",dataKey:"projectInvestmentCashFlow",configKey:"projectInvestmentCashFlow"},{key:"11",tab:"资本金财务现金流量",type:"yearly",dataKey:"equityCapitalCashFlow",configKey:"equityCapitalCashFlow"},{key:"12",tab:"财务计划现金流量",type:"yearly",dataKey:"financialPlanCashFlow",configKey:"financialPlanCashFlow"},{key:"13",tab:"资产负债",type:"yearly",dataKey:"balanceSheet",configKey:"balanceSheet"},{key:"14",tab:"财务指标汇总",type:"basic",dataKey:"financialIndicatorsSummary",configKey:"financialSummary"}],_=e=>e.type==="yearly"?G(b.value):N[e.configKey],g=e=>{var s;return(s=i.resultData)!=null&&s[e.dataKey]?e.type==="yearly"?O(i.resultData[e.dataKey],V[e.configKey],b.value):z(i.resultData[e.dataKey],H[e.configKey]):[]},I=async()=>{if(!i.resultData){x.warning("暂无数据可导出");return}try{d.value=!0,X(c,i.resultData,_,g,"经济分析详情数据.xlsx"),x.success("Excel文件导出成功")}catch(e){console.error("导出失败:",e),x.error("导出失败，请重试")}finally{d.value=!1}};return(e,s)=>{const o=y("a-button"),a=y("a-table"),u=y("a-tab-pane"),h=y("a-tabs");return C(),k("div",Z,[f("div",aa,[ea,t(o,{size:"small",onClick:I,loading:d.value},{icon:r(()=>[t(D(j))]),default:r(()=>[K(" 数据导出 ")]),_:1},8,["loading"])]),t(h,{activeKey:n.value,"onUpdate:activeKey":s[0]||(s[0]=l=>n.value=l),class:"detail-tabs"},{default:r(()=>[(C(),k(A,null,E(c,l=>t(u,{key:l.key,tab:l.tab},{default:r(()=>[t(a,{columns:_(l),"data-source":g(l),pagination:!1,size:"small",bordered:"",scroll:l.type==="yearly"?{x:"max-content"}:void 0},null,8,["columns","data-source","scroll"])]),_:2},1032,["tab"])),64))]),_:1},8,["activeKey"])])}}},oa=w(ta,[["__scopeId","data-v-5b2fb2ef"]]),na={class:"economic-detail"},sa={class:"header-section"},la={class:"title-wrapper"},ra={class:"header-actions"},ia={__name:"index",setup(m){const i=$(),n=R(),d=p(null),v=p(null),b=p({}),c=p(!1),_=p("数据加载中..."),g=async()=>{try{c.value=!0;const{code:o,data:a,msg:u}=await Y({projectId:n.params.projectId,solutionId:n.params.solutionId});console.log("接口返回数据:",a),console.log("lcoh:",a.resultTables.financialIndicatorsSummary.lcoh),console.log("Excel原 lcoh_ori:",a.resultTables.financialIndicatorsSummary.lcoh_ori),console.log("lcoe:",a.resultTables.financialIndicatorsSummary.lcoe),console.log("Excel原 lcoe_ori:",a.resultTables.financialIndicatorsSummary.lcoe_ori),console.log("敏感性分析数据:",a.resultAnalysis),o===0&&(d.value=a.resultTables,v.value=a.resultAnalysis||null)}catch(o){console.error("获取数据失败:",o),d.value=null}finally{c.value=!1}},I=async()=>{try{const{code:o,data:a,msg:u}=await q({projectId:n.params.projectId,solutionId:n.params.solutionId});o===0&&(b.value=a)}catch(o){console.error("获取方案信息失败:",o)}},e=()=>{i.push({path:"/report-detail",query:{projectId:n.params.projectId,solutionId:n.params.solutionId}})},s=async()=>{var a,u;c.value=!0,_.value="报告生成中...";const o=await J(`${location.origin}/report-detail?projectId=${n.params.projectId}&solutionId=${n.params.solutionId}&type=1`);await Q(`/api/v1/cecp/result/export?encodedUrl=${o}`,`${(u=(a=b.value)==null?void 0:a.project)==null?void 0:u.name}.pdf`),setTimeout(()=>{c.value=!1},6500)};return B(async()=>{await Promise.all([g(),I()])}),(o,a)=>{const u=y("a-breadcrumb-item"),h=y("a-breadcrumb"),l=y("a-button"),S=y("a-spin");return C(),k("div",na,[f("div",sa,[f("div",la,[t(h,null,{default:r(()=>[t(u,null,{default:r(()=>[f("a",{href:"void:0",onClick:a[0]||(a[0]=ca=>D(i).back())}," < 经济分析")]),_:1}),t(u,null,{default:r(()=>[K("分析结果")]),_:1})]),_:1})]),f("div",ra,[t(l,{class:"action-btn",onClick:s,loading:c.value},{default:r(()=>[K("下载报告")]),_:1},8,["loading"]),t(l,{type:"primary",class:"action-btn",onClick:e},{default:r(()=>[K("查看报告")]),_:1})])]),t(S,{spinning:c.value,tip:_.value},{default:r(()=>[t(L,{"solution-info":b.value},null,8,["solution-info"]),t(U,{"result-data":d.value},null,8,["result-data"]),t(oa,{"result-data":d.value},null,8,["result-data"]),t(M,{"sensitivity-data":v.value},null,8,["sensitivity-data"])]),_:1},8,["spinning","tip"])])}}},ba=w(ia,[["__scopeId","data-v-c6abf79f"]]);export{ba as default};
