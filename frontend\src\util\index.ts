import dayjs from 'dayjs'
import * as XLSX from 'xlsx'

export const downloadFile = async (url: string, name: string) => {
  const a = document.createElement("a");
  a.href = url;
  a.download = name;
  a.style.display = "none";
  document.body.appendChild(a);
  a.click();
  a.remove();
}

export const formatDate = (timestamp: number) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm') 
}

export const strToBase64 = async (str: string) => {
  const encoder = new TextEncoder();
  const data = encoder.encode(str);
  let binary = '';
  for (let i = 0; i < data.length; i++) {
    binary += String.fromCharCode(data[i]);
  }
  return btoa(binary);
}

export const getOptionByValue = (options: { label: string, value: any }[], value: any) => {
  return options ? options.find(option => option.value === value ) : value;
}

// Excel导出功能
export const exportToExcel = (data: any[], fileName: string = 'export.xlsx') => {
  // 创建工作簿
  const workbook = XLSX.utils.book_new()

  // 如果data是单个表格数据，转换为数组格式
  if (!Array.isArray(data) || !data[0]?.sheetName) {
    const worksheet = XLSX.utils.json_to_sheet(data)
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
  } else {
    // 多个表格数据，每个表格作为一个工作表
    data.forEach((sheetData, index) => {
      const worksheet = XLSX.utils.json_to_sheet(sheetData.data || [])
      const sheetName = sheetData.sheetName || `Sheet${index + 1}`
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)
    })
  }

  // 导出文件
  XLSX.writeFile(workbook, fileName)
}

// 专门用于经济分析详情页的Excel导出
export const exportEconomicDetailToExcel = (
  tableConfigs: any[],
  resultData: any,
  getTableColumns: (config: any) => any[],
  getTableData: (config: any) => any[],
  fileName: string = '经济分析详情数据.xlsx'
) => {
  const workbook = XLSX.utils.book_new()

  tableConfigs.forEach((config: any) => {
    const columns = getTableColumns(config)
    const data = getTableData(config)

    if (data && data.length > 0) {
      // 创建表头
      const headers = columns.map((col: any) => col.title)

      // 创建数据行
      const rows = data.map((row: any) => {
        return columns.map((col: any) => {
          const value = row[col.dataIndex]
          // 处理特殊值
          if (value === '--' || value === null || value === undefined) {
            return ''
          }
          return value
        })
      })

      // 合并表头和数据
      const sheetData = [headers, ...rows]

      // 创建工作表
      const worksheet = XLSX.utils.aoa_to_sheet(sheetData)

      // 设置列宽
      const colWidths = columns.map((col: any) => ({
        wch: Math.max(col.title.length, 15) // 最小宽度15字符
      }))
      worksheet['!cols'] = colWidths

      // 添加到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, config.tab)
    }
  })

  // 导出文件
  XLSX.writeFile(workbook, fileName)
}

const addZero = (str: string, length: number) => {
  const len = str.length
  if (len < length) {
    for (let i = 0; i < length -  len; i++) {
      str = '0' + str
    }
  }
  return str
}

export const saveNunberPoint = (num: number, length: number = 2) => {
  if (num === undefined || num === null) {
    return num
  }
  const len = Math.pow(10, length)
  return Math.round(len * num) / len
}

export const getXDataFromZero = (num: number) => {
  const result: number[] = []
  for (let i = 0; i < num; i++) {
    result.push(i)
  }
  return result
}

