import { defineStore } from 'pinia'
import { getUserInfo } from '@/api/auth'
import { message } from 'ant-design-vue'
// import jsCookie from 'js-cookie'
// import { type UserItem } from '@/api/user'
// import { USERID, EXPIRE_TIME_STAMP } from '@/util/constant'
// te
type UserItem = any
interface UserState {
  userId: number;
  expireTimeStamp: number;
  userInfo: UserItem;
  hasPermissionAccess: boolean;
}
export const isInAdmin = (userInfo: UserItem) => {
  return userInfo.groupInfo?.some(item => item.id === 1)
}

export const canAccessModule = (userInfo: UserItem, moduleId: string, canWrite?: boolean) => {
  if (isInAdmin(userInfo)) return true

  const groupInfo = userInfo.groupInfo
  const result = groupInfo?.some(g => {
    return g.moduleInfo.some(m => moduleId === m.id)
      && g.privilegeInfo.some(p => {
        if (canWrite) {
          return p.id === 2
        } else {
          return [1, 2].includes(p.id)
        }
      })
  })
  return result
}

export const useUserInfo = defineStore('user', {
  state: (): any => ({
    // userId: parseInt(jsCookie.get(USERID), 10),
    // expireTimeStamp: parseInt(jsCookie.get(EXPIRE_TIME_STAMP), 10),
    userInfo: {} as UserItem,
    hasPermissionAccess: false
  }),
  getters: {
    isAdmin: (state: UserState) => isInAdmin(state.userInfo),

  },
  actions: {
    async fetchUserInfo() {
      const { code, msg, data } = await getUserInfo()
      if (code === 0) {
        this.userInfo = data
      } else {
        message.error(msg)
      }
    },
    setPagePermission(permission: boolean) {
      this.hasPermissionAccess = permission
    }
  },
})