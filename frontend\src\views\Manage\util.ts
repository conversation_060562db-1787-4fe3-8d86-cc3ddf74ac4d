// 项目信息
export const formProjectInfo = [
  { label: '客户名称', name: 'customer', default: undefined, rules: [{ required: true }], type: 'string' },
  { label: '项目名称', name: 'projectName', default: undefined,  rules: [{ required: true }], type: 'string' },
  { label: '项目背景', name: 'projectBackground', default: undefined,  rules: [{ required: true }], type: 'string' },
  { label: '客户需求', name: 'requirementDesc', default: undefined,  rules: [{ required: true }], type: 'string' },
]
// 限制性指标
export const formRestricIndicator = [
  { label: '电解槽最低负载', name: 'minLoadOfAlk', default: 0.3, rules: [{ required: true }], type: 'number', unit: '' },
  { label: '储能充电效率', name: 'rateOfCharge', default: 0.94, rules: [{ required: true }], type: 'number', unit: '' },
  { label: '储能放电效率', name: 'rateOfDischarge', default: 0.92, rules: [{ required: true }], type: 'number', unit: ''},
  { label: '制氢电源效率', name: 'rateOfH2Power', default: 0.97, rules: [{ required: true }], type: 'number', unit: '' },
  { label: '辅助系统能耗', name: 'ecOfAssitSys', default: 0.3, rules: [{ required: true }], type: 'number', unit: 'kwh/nm3' },
  { label: '电解槽年衰减率', name: 'dampRateOfAlk', default: 0.005, rules: [{ required: true }], type: 'number', unit: '' },
  { label: '光伏年衰减率', name: 'dampRateOfPv', default: 0.0055, rules: [{ required: true }], type: 'number', unit: '' },
  { label: '储能配置置信度', name: 'batConfidence', default: 0.996, rules: [{ required: true }], type: 'number', unit: 'h' },
  { label: '储能充放电深度', name: 'batDepth', default: 0.94, rules: [{ required: true }], type: 'number', unit: '元/kwh' },
  { label: '储能初始SoC比例', name: 'soc', default: 0.2, rules: [{ required: true }], type: 'number', unit: '' },
  { label: '储能最低配置比例', name: 'minConfigRateOfBat', default: 0.2137, rules: [{ required: true }], type: 'number', unit: '' },
  { label: '最大上网比例', name: 'maxRate', default: 0.2, rules: [{ required: true }], type: 'number', unit: '' },
  { label: '电解槽启动浪费氢气', name: 'wasteH2', default: 200, rules: [{ required: true }], type: 'number', unit: '' },
  { label: '储能充放电倍率', name: 'batMagnification', default: 0.5, rules: [{ required: true }], type: 'number', unit: '' },
]
// 基本参数配置
export const formBasicConfig = [
  { label: '年产氢量', name: 'hpy', default: undefined, rules: [{ required: true }], type: 'number', unit: '万吨' },
  { label: '负荷缺失率', name: 'lossRateOfLoad', default: 0, rules: [{ required: true }], type: 'number', unit: '' },
  { label: '电解槽额定能耗', name: 'fixedEcOfAlk', default: undefined, rules: [{ required: true }], type: 'number', unit: 'kwh/nm3' },
  { label: '算法迭代次数', name: 'iterations', default: 50, rules: [{ required: true }], type: 'number', unit: '' }, // >= 20
]
// 风光氢容量配置
export const formPwhStorage = [
  { label: '光伏容量上限', name: 'pvUL', default: undefined, rules: [{ required: true }], type: 'number', unit: 'MW' },
  { label: '光伏容量下限', name: 'pvLL', default: undefined, rules: [{ required: true }], type: 'number', unit: 'MW' },
  { label: '储能容量上限', name: 'batUL', default: undefined, rules: [{ required: true }], type: 'number', unit: 'MWh' },
  { label: '储能容量下限', name: 'batLL', default: undefined, rules: [{ required: true }], type: 'number', unit: 'MWh' },
  { label: '风电容量上限', name: 'wpUL', default: undefined, rules: [{ required: true }], type: 'number', unit: 'MW' },
  { label: '风电容量下限', name: 'wpLL', default: undefined, rules: [{ required: true }], type: 'number', unit: 'MW' },
  { label: '电解槽容量上限', name: 'alkUL', default: undefined, rules: [{ required: true }], type: 'number', unit: 'MW' },
  { label: '电解槽容量下限', name: 'alkLL', default: undefined, rules: [{ required: true }], type: 'number', unit: 'MW' },
]
// 投资指标
export const formInvestIndicator = [
  { label: '光伏EPC投资', name: 'pvEpcInvest', default: 2.9, rules: [{ required: true }], type: 'number', unit: '元/W' },
  { label: '光伏运营成本', name: 'pvOpertCost', default: 0.25, rules: [{ required: true }], type: 'number', unit: '元/W/年' },
  { label: '储能EPC投资', name: 'batEpcInvest', default: 4.2, rules: [{ required: true }], type: 'number', unit: '元/W' },
  { label: '储能运营成本', name: 'batOpertCost', default: 0.3, rules: [{ required: true }], type: 'number', unit: '元/W/年' },
  { label: '风电EPC投资', name: 'wpEpcInvest', default: 1.3, rules: [{ required: true }], type: 'number', unit: '元/W' },
  { label: '风电运营成本', name: 'wpOpertCost', default: 0.0675, rules: [{ required: true }], type: 'number', unit: '元/W/年' },
  { label: '制氢系统投资', name: 'h2SysInvest', default: 2, rules: [{ required: true }], type: 'number', unit: '元/W' },
  { label: '制氢运维比例', name: 'h2SysOpertRadio', default: 0.02, rules: [{ required: true }], type: 'number', unit: '' },
  { label: '制氢厂房投资', name: 'h2PlantInvest', default: 1.5, rules: [{ required: true }], type: 'number', unit: '元/W' },
]
// 运营指标
export const formOpertIndicator = [
  { label: '项目周期', name: 'projCycle', default: 25, rules: [{ required: true }], type: 'number', unit: '年' },
  { label: '贷款比例', name: 'loanRadio', default: 0.7, rules: [{ required: true }], type: 'number', unit: '' },
  { label: '贷款周期', name: 'loanCycle', default: 15, rules: [{ required: true }], type: 'number', unit: '年' },
  { label: '贷款利率', name: 'loanRate', default: 0.049, rules: [{ required: true }], type: 'number', unit: '' },
  { label: '用水价格', name: 'waterPrice', default: 4.38, rules: [{ required: true }], type: 'number', unit: '元/吨' },
  { label: '制氢耗水量', name: 'waterCost', default: 1.4, rules: [{ required: true }], type: 'number', unit: 'L/Nm3' },
  { label: '网购电平均价', name: 'avgPriceOfGrid', default: 0.45, rules: [{ required: true }], type: 'number', unit: '元/kwh' },
  { label: '网购电最大小时数', name: 'maxHourOfGrid', default: 0, rules: [{ required: true }], type: 'number', unit: 'h' },
  { label: '绿电上网价格', name: 'priceOfGreenElectric', default: 0.332, rules: [{ required: true }], type: 'number', unit: '元/kwh' },
  { label: '折现率', name: 'discountRate', default: 0.06, rules: [{ required: true }], type: 'number', unit: '' },
]
