// green
import step1_green from '@/assets/imgs/step1.png'
import step2_green from '@/assets/imgs/step2.png'
import step3_green from '@/assets/imgs/step3.png'
import p1_green from '@/assets/imgs/projectDetail/baseinfo.svg'
import p2_green from '@/assets/imgs/projectDetail/power_stat.svg'
import p3_green from '@/assets/imgs/projectDetail/h2_stat.svg'
import p4_green from '@/assets/imgs/projectDetail/hour_stat.svg'
import p5_green from '@/assets/imgs/projectDetail/alk_stat.svg'
import p6_green from '@/assets/imgs/projectDetail/store_h2_stat.svg'

// blue
import step1_blue from '@/assets/imgs/step1-blue.png'
import step2_blue from '@/assets/imgs/step2-blue.png'
import step3_blue from '@/assets/imgs/step3-blue.png'
import p1_blue from '@/assets/imgs/projectDetail-blue/baseinfo.svg'
import p2_blue from '@/assets/imgs/projectDetail-blue/power_stat.svg'
import p3_blue from '@/assets/imgs/projectDetail-blue/h2_stat.svg'
import p4_blue from '@/assets/imgs/projectDetail-blue/hour_stat.svg'
import p5_blue from '@/assets/imgs/projectDetail-blue/alk_stat.svg'
import p6_blue from '@/assets/imgs/projectDetail-blue/store_h2_stat.svg'

// THEME:
// green
export const greenConfig = {
  // 基础
  title: '综合能源规划设计系统',
  company: '隆基氢能',
  logo: '/logo.svg',
  face: '',
  apiBase: '/api/v1',

  // 绿色主题
  baseColor: '#15a675', // green-#15a675
  homeBarChartColor: '#5b84e3',
  step1: step1_green,
  step2: step2_green,
  step3: step3_green,
  p1: p1_green,
  p2: p2_green,
  p3: p3_green,
  p4: p4_green,
  p5: p5_green,
  p6: p6_green,
}

// blue
export const blueConfig = {
  // 基础
  title: '绿电制绿氢规划设计软件',
  company: '',
  logo: '/logo.svg',
  face: '',
  apiBase: '/api/v1',

  // 蓝色主题
  baseColor: '#0360B0', // blue-#084B9C, zh-#0360B0
  homeBarChartColor: '#5b84e3',
  step1: step1_blue,
  step2: step2_blue,
  step3: step3_blue,
  p1: p1_blue,
  p2: p2_blue,
  p3: p3_blue,
  p4: p4_blue,
  p5: p5_blue,
  p6: p6_blue,
}

const envMap = {
  prod: greenConfig,
  test: greenConfig,
  zh: blueConfig,
}
// console.log('__APP_ENV__:', __APP_ENV__,  import.meta.env)

export const baseConfig = envMap[import.meta.env.VITE_APP_ENV] || greenConfig

