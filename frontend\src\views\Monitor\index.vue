<template>
  <div class="body_wrap">
    <a-spin :spinning="loading">
      <div class="title1">
        策略状态
      </div>
      <div class="real_data_wrap">
        <div class="r_item"><span class="r_title">策略名称：</span>多槽联动寿命优先策略</div>
        <div class="r_item"><span class="r_title">状态：</span>运行中</div>
        <div class="r_item"><span class="r_title">执行时间：</span>2024.9.1-2025.9.2</div>
        <div class="r_item"><span class="r_title">循环次数：</span>91</div>
      </div>
      <div class="load_line_wrap">
        <div class="load_card">
          <div class="card_title">动态负荷功率</div>
          <div class="load_line_chart">
            <LineChart class="chart_content" v-if="loadPlanData" :data="loadPlanData"/>
          </div>
        </div>
        <div class="load_card">
          <div class="card_title">实时指标</div>
          <div class="load_line_chart c_right_wrap">
            <div v-for="item in realData" class="r_wrap">
              <div class="r_title">{{ item.title }}</div>
              <div class="r_content">{{ item.value }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="chart_tab">
        <div class="tab_box_wrap">
          <div class="t_card"
            v-for="item in tabTitleData" @click="selTab(item.key)"
            :class="{ t_card_sel: tabCurKey === item.key }"
          >
            <div class="t_left">
              <div class="title">{{ item.title }}</div>
              <div class="value">{{ item.value }}</div>
            </div>
          </div>
        </div>
        <div class="status_wrap">
          <div class="status_color"></div>
        </div>
        <div>
          <div v-if="tabCurKey === 'pv'">
            <div class="chart_content_wrap">
              <div class="chart_wrap single_chart_wrap">
                <LineChart class="chart_content" v-if="pvData" :data="pvData"/>
              </div>
            </div>
          </div>
          <div v-else-if="tabCurKey === 'wind'">
            <div class="chart_content_wrap">
              <div class="chart_wrap single_chart_wrap">
                <LineChart class="chart_content" v-if="windData" :data="windData"/>
              </div>
            </div>
          </div>
          <div v-else-if="tabCurKey === 'grid'">
            <div class="chart_content_wrap">
              <div class="chart_wrap single_chart_wrap">
                <LineChart class="chart_content" v-if="gridData" :data="gridData"/>
              </div>
            </div>
          </div>
          <div v-else-if="tabCurKey === 'bat'">
            <div class="chart_content_wrap">
              <div class="chart_wrap single_chart_wrap">
                <LineChart class="chart_content" v-if="batData" :data="batData"/>
              </div>
            </div>
          </div>
          <div v-else-if="tabCurKey === 'alk'">
            <div class="status_tag_wrap">
              <div class="tab_items">
                <a-tag color="#888">停机</a-tag>
                <a-tag color="#87d068">运行</a-tag>
              </div>
            </div>
            <div class="chart_content_wrap">
              <div class="chart_wrap">
                <LineChart class="chart_content" v-if="alkData1" :data="alkData1"/>
              </div>
              <div class="chart_wrap">
                <LineChart class="chart_content" v-if="alkData2" :data="alkData2"/>
              </div>
              <div class="chart_wrap">
                <LineChart class="chart_content" v-if="alkData3" :data="alkData3"/>
              </div>
              <div class="chart_wrap">
                <LineChart class="chart_content" v-if="alkData4" :data="alkData4"/>
              </div>
            </div>
          </div>
        </div>
      
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import LineChart from './LineChart/index.vue'
import { alkP1, alkP2, alkP3, alkP4, loadPlan } from './util/test'
import {
  createALKOptions, createLoadOptions,
  createSingleLineOptions
} from './util/index.js'
import { getPlan, getReal } from '@/api/strategy'

const tabCurKey = ref('alk')
const alkData1 = ref()
const alkData2 = ref()
const alkData3 = ref()
const alkData4 = ref()
const pvData = ref()
const windData = ref()
const gridData = ref()
const batData = ref()

const loadPlanData = ref()
const loading = ref(false)

const realData = [
  { title: '调度下达率', value: '99.95%' },
  { title: '功率损耗率', value: '0.75%' },
  { title: '风光消纳率', value: '81.08%' },
  { title: '储能SOH', value: '100%' },
  { title: '系统效率', value: '59.95%' },
]

const tabTitleData = [
  { title: '光伏(实时功率)', value: '5000KW', icon: '', key: 'pv' },
  { title: '风电(实时功率)', value: '0KW', icon: '', key: 'wind' },
  { title: '电网(实时功率)', value: '-1500KW', icon: '', key: 'grid' },
  { title: '储能(实时功率)', value: '-500KW', icon: '', key: 'bat' },
  { title: '制氢(实时功率)', value: '3000KW', icon: '', key: 'alk' },
]

const selTab = (tabKey) => {
  console.log('key:', tabKey)
  tabCurKey.value = tabKey
}
const getDataByDevId = (data, id) => {
  return data?.find(item => item.dev_id === id)?.data
}
const initData = async() => {
  loading.value = true
  const { data: planData } = await getPlan()
  const { data: realData } = await getReal()
  loading.value = false


  alkData1.value = createALKOptions({
    title: 'ALKHi 01 电解槽',
    plan: getDataByDevId(planData, 1),
    real: getDataByDevId(realData, 1)
  })
  alkData2.value = createALKOptions({
    title: 'ALKHi 02 电解槽',
    plan: getDataByDevId(planData, 2),
    real: getDataByDevId(realData, 2)
  })
  alkData3.value = createALKOptions({
    title: 'ALKHi 03 电解槽',
    plan: getDataByDevId(planData, 3),
    real: getDataByDevId(realData, 3)
  })
  alkData4.value = createALKOptions({
    title: 'ALKHi 04 电解槽',
    plan: getDataByDevId(planData, 4),
    real: getDataByDevId(realData, 4)
  })
  // 
  pvData.value = createSingleLineOptions({
    title: '光伏',
    plan: getDataByDevId(planData, 5),
  })
  windData.value = createSingleLineOptions({
    title: '风电',
    plan: getDataByDevId(planData, 6),
  })
  gridData.value = createSingleLineOptions({
    title: '电网',
    plan: getDataByDevId(planData, 7),
  })
  batData.value = createSingleLineOptions({
    title: '储能',
    plan: getDataByDevId(planData, 8),
  })


  // console.log("plan:", planData, realData)
}
onMounted(() => {
  // alkData1.value = createALKOptions(alkP1)
  // alkData2.value = createALKOptions(alkP2)
  // alkData3.value = createALKOptions(alkP3)
  // alkData4.value = createALKOptions(alkP4)

  loadPlanData.value = createLoadOptions(loadPlan)
  initData()

})
</script>

<style lang="less" scoped>
@import '@/style/base.less';

.body_wrap {
  padding: 10px 20px;
}
.real_data_wrap {
  display: flex;
  justify-content: space-between;
  background: #fff;
  margin: 10px 0;
  padding: 15px 15px;
  .r_item {
    font-size: 16px;
    font-weight: 500;
    .r_title {
      font-weight: 600;
    }
  }
}
.load_line_wrap {
  display: flex;
  .card_title {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
  }
  .load_card {
    width: 50%;
    margin-right: 20px;
    border: 1px solid #eee;
    box-shadow: 5px 5px 5px #ddd;
    padding: 15px;
    background: #fff;
    &:last-child {
      margin-right: 0;
    }
  }
  .load_line_chart {
    height: 150px;
  }
  .c_right_wrap {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  .r_wrap {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
    box-shadow: 5px 5px 10px #ddd;
    border: 1px solid #efefef;
    height: 60%;
    &:last-child {
      margin-right: 0;
    }
    .r_content {
      font-size: 20px;
      font-weight: bold;
    }
    .r_title {
      width: 100%;
      display: flex;
      justify-content: center;
      font-size: 14px;
      color: #999;
    }
  }
}

.chart_tab {
  margin-top: 15px;
  .tab_box_wrap {
    display: flex;
  }
  .t_card {
    width: 50%;
    background: #dce8fa;
    margin-right: 15px;
    padding: 20px 10px;
    &:last-child{
      margin-right: 0;
    }
    .t_left {
      .value {
        margin-top: 10px;
        font-size: 18px;
        font-weight: bold;
      }
      .title {
        color: #555;
      }
    }
    border-radius: 5px;
    border: 2px solid transparent;
    // transition: all 0.5s;
    &:hover {
      cursor: pointer;
      border: 2px solid #71a2eb;
    }
  }
  .t_card_sel {
    border: 2px solid #71a2eb;
  }
}
.status_tag_wrap {
  display: flex;
  justify-content: flex-end;
  margin: 10px 0;
}
.chart_content_wrap {
  // margin: 20px 0 0 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}
.chart_wrap {
  width: 48%;
  height: 28vh;
  min-height: 300px;
  // height: calc(100vh - 900px);
  // border: 1px solid #71a2eb;
  margin: 0 10px 30px 0;
  background: #fff;
  padding: 15px 0;
}
.single_chart_wrap {
  width: 80%;
  height: calc(100vh - 600px);
  margin: 30px 0;
}
.chart_content {

}

</style>
