server:
  port: 20180
  servlet:
    session:
      timeout: 86400  # session 的有效期， 默认值30m
      cookie:
        name: session_id # 自定义的session名称
        max-age: 86300  # cookie的有效期
database:
  file-path: "E:/MT_PROJECT/code/kit/backend/gsct/db" # 数据库(sqlite)文件路径

calc:
  timeout: 30 # 计算任务超时时长，单位：分钟
  result:
    base-dir: "E:/MT_PROJECT/code/kit/backend/gsct" # 计算入参/出参保存路径

#  session.timeout
#  session.timeout 是指服务器端会话的超时设置。这个超时通常是指在客户端最后一次与服务器交互后，服务器上为该客户端保存的会话数据（session data）可以存活的时间。一旦超过这个时间，服务器将认为会话已结束，并可能删除存储在服务器上的会话信息。
#
#  应用对象：服务器端的会话存储。
#  作用方式：通常由服务器的会话管理机制控制，比如在HTTP中的会话是用一个名为JSESSIONID的cookie来识别的（在使用JSP/Servlet容器时），服务器会计算这个会话的空闲时间，如果达到session.timeout的设定值，则会话被销毁。
#  配置位置：在服务器配置中设置，例如在Java的web应用中，通常在web.xml中进行设置。

#  cookie.max-age
#  cookie.max-age 属性定义了一个cookie的生命周期，指定了该cookie存在的最大秒数。当设置了max-age属性后，浏览器将在指定秒数过后自动删除这个cookie。max-age是一个相对时间，从浏览器接收到带有这个max-age的cookie时开始计时。
#
#  应用对象：客户端的cookie存储。
#  作用方式：这是一个cookie的属性，可以通过HTTP响应头Set-Cookie设置，并告诉客户端如何存储该cookie。它直接影响客户端存储的cookie，一旦cookie到期，客户端就不会在随后的请求中包含这个cookie。
#  配置位置：在生成cookie并发送到客户端的HTTP响应中设置。

#  区别
#  作用层次：session.timeout 作用于服务器端的会话，而cookie.max-age作用于客户端存储的cookie。
#  控制位置：session.timeout 由服务器控制，cookie.max-age由发送给客户端的HTTP响应控制。
#  目标对象：session.timeout 控制服务器关于会话的信息何时清除，cookie.max-age 控制浏览器何时删除特定cookie。
#  在实践中，这两个设置可以一起使用来控制用户的登录状态。例如，如果一个会话cookie (JSESSIONID) 没有设置max-age或者设置为会话级别（即关闭浏览器删除），但服务器端的session.timeout设为30分钟，用户在30分钟内没有任何操作，那么即使客户端的cookie还存在，服务器端的会话数据可能会被清除，用户要再次访问服务器资源时，可能需要重新登录。
#

