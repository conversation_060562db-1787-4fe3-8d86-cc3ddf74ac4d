export const urlPrefix = '/api/v1'
export const initPolicy = (app) => {
  app.post(`${urlPrefix}/logout`, (_, res) => {
    res.send({
      code: 0,
      message: 'ok',
      data: {
      }
    });
  })
  app.post(`${urlPrefix}/login`, (_, res) => {
    res.send({
      code: 0,
      message: 'ok',
      data: {
      }
    });
  })
  app.post(`${urlPrefix}/submitTask`, (_, res) => {
    res.send({
      code: 0,
      message: 'ok',
      data: {
      }
    });
  })

  app.get(`${urlPrefix}/getUser`, (_, res) => {
    res.send({
      code: 0,
      message: 'ok',
      data: {
        id: 0,
        userName: 'kiwiyan',
        nickName: 'kiwi',
        role: 1
      }
    });
  })
  // policy
  app.get(`${urlPrefix}/policyManager/policy`, (req, res) => {
    const resData = [
      { id: 1, name: '经济最优智能策略', status: 1, desc: '经济最优智能', score: 90, creator: 'kiwi', period: '2024.9.18-2024.11.28', createAt: '2024.9.18' },
      { id: 1, name: '轮值优化策略', status: 0, desc: '轮值优化', score: 93, creator: 'jack', period: '2024.9.18-2024.11.29', createAt: '2024.9.18' },
      { id: 1, name: '最少停机次数策略', status: 0, desc: '最少停机次数', score: 97, creator: 'rose', period: '2024.9.19-2024.11.28', createAt: '2024.9.18' },
      { id: 1, name: '最大产氢量策略', status: 0, desc: '最大产氢量', score: 88, creator: 'nancy', period: '2024.7.18-2024.12.28', createAt: '2024.9.18' },
    ]
    setTimeout(() => {
      res.send({
        code: 0,
        msg: 'ok',
        data: resData
      });
    }, 300)
  })

  app.post(`${urlPrefix}/policyManager/policy`, (_, res) => {
    res.send({
      code: 0,
      message: 'ok',
      data: {
      }
    });
  })

  app.delete(`${urlPrefix}/policyManager/policy/:policyId/delete`, (_, res) => {
    res.send({
      code: 0,
      message: 'ok',
      data: {
      }
    });
  })

  // 
  app.get(`${urlPrefix}/policyManager/policy`, (req, res) => {
    const resData = [
      { id: 1, name: '经济最优智能策略', status: 1, desc: '经济最优智能', score: 90, creator: 'kiwi', period: '2024.9.18-2024.11.28', createAt: '2024.9.18' },
      { id: 1, name: '轮值优化策略', status: 0, desc: '轮值优化', score: 93, creator: 'jack', period: '2024.9.18-2024.11.29', createAt: '2024.9.18' },
      { id: 1, name: '最少停机次数策略', status: 0, desc: '最少停机次数', score: 97, creator: 'rose', period: '2024.9.19-2024.11.28', createAt: '2024.9.18' },
      { id: 1, name: '最大产氢量策略', status: 0, desc: '最大产氢量', score: 88, creator: 'nancy', period: '2024.7.18-2024.12.28', createAt: '2024.9.18' },
    ]
    setTimeout(() => {
      res.send({
        code: 0,
        msg: 'ok',
        data: resData
      });
    }, 300)
  })
  // 
  app.get(`${urlPrefix}/operationManager/plan`, (req, res) => {
    const resData = [
      {
        id: 1,
        dev_id: 1,
        data: [20, 182, 191, 234, 0, 330, 0,120, 132, 101, 134, 0, 0, 210, 120, 132, 101, 134, 0, 0, 210,120, 132, 101]
      },
      {
        id: 2,
        dev_id: 2,
        data: [20, 182, 191, 234, 0, 330, 0,120, 132, 101, 134, 0, 0, 210, 120, 132, 101, 134, 0, 0, 210,120, 132, 101]
      },
      {
        id: 3,
        dev_id: 3,
        data: [20, 182, 191, 234, 0, 330, 0,120, 132, 101, 134, 0, 0, 210, 120, 132, 101, 134, 0, 0, 210,120, 132, 101]
      },
      {
        id: 4,
        dev_id: 4,
        data: [20, 182, 191, 234, 0, 330, 0,120, 132, 101, 134, 0, 0, 210, 120, 132, 101, 134, 0, 0, 210,120, 132, 101]
      },
      {
        id: 5,
        dev_id: 5,
        data: [20, 182, 191, 234, 0, 330, 0,120, 132, 101, 134, 0, 0, 210, 120, 132, 101, 134, 0, 0, 210,120, 132, 101]
      },
      {
        id: 6,
        dev_id: 6,
        data: [20, 182, 191, 234, 0, 330, 0,120, 132, 101, 134, 0, 0, 210, 120, 132, 101, 134, 0, 0, 210,120, 132, 101]
      },
      {
        id: 7,
        dev_id: 7,
        data: [20, 182, 191, 234, 0, 330, 0,120, 132, 101, 134, 0, 0, 210, 120, 132, 101, 134, 0, 0, 210,120, 132, 101]
      },
      {
        id: 8,
        dev_id: 8,
        data: [20, 182, 191, 234, 0, 330, 0,120, 132, 101, 134, 0, 0, 210, 120, 132, 101, 134, 0, 0, 210,120, 132, 101]
      },
    ]
    setTimeout(() => {
      res.send({
        code: 0,
        msg: 'ok',
        data: resData
      });
    }, 300)
  })

  app.get(`${urlPrefix}/operationManager/measure`, (req, res) => {
    const resData = [
      {
        id: 1,
        dev_id: 1,
        data: [30, 192, 391, 434, 10, 430, 10,220, 182, 191, 334, 10, 0, 310, undefined, undefined, undefined, undefined,undefined,undefined, undefined, undefined, undefined, undefined]
      },
      {
        id: 2,
        dev_id: 2,
        data: [30, 192, 391, 434, 10, 430, 10,220, 182, 191, 334, 10, 0, 310, undefined, undefined, undefined, undefined,undefined,undefined, undefined, undefined, undefined, undefined]
      },
      {
        id: 3,
        dev_id: 3,
        data: [30, 192, 391, 434, 10, 430, 10,220, 182, 191, 334, 10, 0, 310, undefined, undefined, undefined, undefined,undefined,undefined, undefined, undefined, undefined, undefined]
      },
      {
        id: 4,
        dev_id: 4,
        data: [30, 192, 391, 434, 10, 430, 10,220, 182, 191, 334, 10, 0, 310, undefined, undefined, undefined, undefined,undefined,undefined, undefined, undefined, undefined, undefined]
      },
      {
        id: 5,
        dev_id: 5,
        data: [30, 192, 391, 434, 10, 430, 10,220, 182, 191, 334, 10, 0, 310, undefined, undefined, undefined, undefined,undefined,undefined, undefined, undefined, undefined, undefined]
      },
      {
        id: 6,
        dev_id: 6,
        data: [30, 192, 391, 434, 10, 430, 10,220, 182, 191, 334, 10, 0, 310, undefined, undefined, undefined, undefined,undefined,undefined, undefined, undefined, undefined, undefined]
      },
      {
        id: 7,
        dev_id: 7,
        data: [30, 192, 391, 434, 10, 430, 10,220, 182, 191, 334, 10, 0, 310, undefined, undefined, undefined, undefined,undefined,undefined, undefined, undefined, undefined, undefined]
      },
      {
        id: 8,
        dev_id: 8,
        data: [30, 192, 391, 434, 10, 430, 10,220, 182, 191, 334, 10, 0, 310, undefined, undefined, undefined, undefined,undefined,undefined, undefined, undefined, undefined, undefined]
      },
    ]
    setTimeout(() => {
      res.send({
        code: 0,
        msg: 'ok',
        data: resData
      });
    }, 300)
  })
  
}
