<template>
  <div class="project-info">
    <h2 class="section-title">项目信息</h2>
    <div class="info-grid">
      <div 
        v-for="item in projectInfoItems" 
        :key="item.label" 
        class="info-item"
      >
        <span class="label">{{ item.label }}</span>
        <span class="value">{{ item.value }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
const props = defineProps({
  solutionInfo: {
    type: Object,
    default: () => ({})
  }
})

const projectInfoItems = computed(() => [
  { label: '项目名称：', value: props.solutionInfo?.project?.name, type: 'static' },
  { label: '客户名称：', value: props.solutionInfo?.project?.customer, type: 'static' },
  { label: '项目周期：', value: `${props.solutionInfo?.calcParams?.operatingYears}年`, type: 'static' },
  { label: '贷款周期：', value: `${props.solutionInfo?.calcParams?.loanTerm}年`, type: 'static' },
  { label: '项目描述：', value: props.solutionInfo?.project?.desc, type: 'static' },
])
</script>

<style scoped>
@import '@/style/base.less';

.project-info {
  background: white;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 12px;
  color: #333;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px 24px;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 13px;
}

.label {
  color: #666;
  min-width: 80px;
}

.value {
  color: #333;
}

/* PDF导出时的样式优化 */
:deep(.pdf-export-mode) .project-info {
  margin-bottom: 20px;
  page-break-inside: avoid;
}

:deep(.pdf-export-mode) .section-title {
  background: #f5f5f5;
  padding: 10px;
  margin-bottom: 15px;
  border: 1px solid #e8e8e8;
  font-weight: bold;
}
</style> 