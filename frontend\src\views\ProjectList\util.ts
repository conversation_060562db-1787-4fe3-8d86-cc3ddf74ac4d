import { useI18n } from 'vue-i18n'

export const capColumns = () => {
  const { t } = useI18n()
  return [
    {
      title: t('projectList.columns.projectName'),
      dataIndex: 'name',
      // customCell: (_, index) => {
      //   // console.log('rr:', _)
      //   if (index == 1) {
      //     return {
      //       rowSpan: 3,
      //       // colSpan: 2,
      //     };
      //   }
      //   if (index >=2 && index <=3) {
      //     return {
      //       rowSpan: 0
      //     }
      //   }
      // },
      width: '100px'
    },
    {
      title: t('projectList.columns.customerName'),
      dataIndex: 'customer',
      width: '100px'
    },
    {
      title: t('projectList.columns.projectBackground'),
      dataIndex: 'desc',
      width: '140px'
    },
    {
      title: t('projectList.columns.projectAction'),
      dataIndex: 'action',
      key: 'action',
      width: '120px'
    },
    {
      title: t('projectList.columns.solution'),
      dataIndex: '',
      key: 'solution',
      width: '65%'
    },
  ];
}
export const innerColumns = () => {
  const { t } = useI18n()
  return [
    {
      title: t('projectList.columns.solutionName'),
      dataIndex: 'name',
      key: 'name',
      width: '100px'
    },
 
    {
      title: t('projectList.columns.scenario'),
      dataIndex: 'topology',
      key: 'topology',
      width: '200px'
    },
    {
      title: t('projectList.columns.solveTarget'),
      dataIndex: 'targetExpr',
      key: 'targetExpr',
      width: '140px'
    },
    {
      title: t('projectList.columns.createTime'),
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: t('projectList.columns.status'),
      dataIndex: 'status',
      key: 'status',
      width: '100px'
    },
    {
      title: t('common.action.action'),
      dataIndex: 'action',
      key: 'action',
      width: '490px'
    }
  ]
}

export const projectStatus = () => {
  const { t } = useI18n()
  return [
    { label: t('projectList.status.running'), value: 1, color: 'processing' },
    { label: t('projectList.status.success'), value:  2, color: 'success' },
    { label: t('projectList.status.failed'), value: 3, color: 'error'  },
  ]
}

export const target = () => {
  const { t } = useI18n()
  return [
    t('projectList.targets.lcoh'),
    t('projectList.targets.lowestInvestment'),
    t('projectList.targets.lowestAbandonRate'),
    t('projectList.targets.maxHydrogenProduction')
  ]
}

export const topo = () => {
  const { t } = useI18n()
  return [
    t('projectList.topology.photovoltaic'),
    t('projectList.topology.windTurbine'),
    t('projectList.topology.grid'),
    t('projectList.topology.energyStorage'),
    t('projectList.topology.hydrogenProduction'),
    t('projectList.topology.hydrogenStorage'),
  ]
}