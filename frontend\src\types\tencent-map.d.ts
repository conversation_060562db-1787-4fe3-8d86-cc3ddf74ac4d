// 腾讯地图 TypeScript 类型声明

declare global {
  interface Window {
    TMap: any;
    onTMapCallback: () => void;
  }
}

declare namespace TMap {
  class Map {
    constructor(container: string | HTMLElement, options: MapOptions);
    setCenter(center: LatLng): void;
    setZoom(zoom: number): void;
    on(event: string, callback: (evt: any) => void): void;
    destroy(): void;
  }

  class LatLng {
    constructor(lat: number, lng: number);
    lat: number;
    lng: number;
  }

  class MultiMarker {
    constructor(options: MultiMarkerOptions);
    setMap(map: Map | null): void;
    setPosition(position: LatLng): void;
  }

  class MarkerStyle {
    constructor(options: MarkerStyleOptions);
  }

  namespace service {
    class Geocoder {
      constructor();
      getAddress(options: GeocodeOptions): void;
    }

    class SearchService {
      constructor(options: SearchServiceOptions);
      search(options: SearchOptions): void;
    }
  }

  interface MapOptions {
    center?: LatLng;
    zoom?: number;
    mapStyleId?: string;
  }

  interface MultiMarkerOptions {
    map: Map;
    styles: { [key: string]: MarkerStyle };
    geometries: MarkerGeometry[];
  }

  interface MarkerStyleOptions {
    width: number;
    height: number;
    anchor: { x: number; y: number };
    src: string;
  }

  interface MarkerGeometry {
    id: string;
    styleId: string;
    position: LatLng;
    properties: {
      title: string;
    };
  }

  interface GeocodeOptions {
    location: LatLng;
    success: (result: any) => void;
    error: (error: any) => void;
  }

  interface SearchServiceOptions {
    complete: (result: any) => void;
    error: (error: any) => void;
  }

  interface SearchOptions {
    keyword: string;
    region: string;
  }
}

export {};
