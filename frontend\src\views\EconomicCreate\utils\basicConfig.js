// 基本信息表单配置
export const formBasicInfo = () => [
  {
    label: '项目名称',
    name: 'projectName',
    unit: '',
    type: 'project-select',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '客户名称',
    name: 'customerName',
    unit: '',
    type: 'string',
    rules: [{ required: false, message: '请输入' }],
    visible: true
  },
  {
    label: '项目运营期',
    name: 'operatingYears',
    unit: '年',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '贷款年限',
    name: 'loanTerm',
    unit: '年',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '项目场景',
    name: 'projectScene',
    unit: '',
    type: 'checkbox-group',
    rules: [{ required: true, message: '请至少选择一项' }],
    default: [1, 1, 0, 1, 1, 0], // [光伏,风电,电网,储能,制氢,储氢]
    visible: true,
    options: [
      { label: '光伏', value: 'pv', index: 0 },
      { label: '风电', value: 'wind', index: 1 },
      { label: '电网', value: 'grid', index: 2 },
      { label: '储能', value: 'storage', index: 3 },
      { label: '制氢', value: 'hydrogen', index: 4 },
      { label: '储氢', value: 'hydrogenStorage', index: 5 }
    ]
  },
  {
    label: '项目描述',
    name: 'desc',
    unit: '',
    type: 'textarea',
    rules: [{ required: false, message: '请输入' }],
    visible: true
  },
  {
    label: '方案描述',
    name: 'solutionDesc',
    unit: '',
    type: 'textarea',
    rules: [{ required: false, message: '请输入' }],
    visible: true
  },
  {
    label: 'test title',
    name: 'testTitle',
    unit: '',
    type: 'string',
    rules: [{ required: false, message: '请输入' }],
    visible: false
  }
]

// 运营参数配置
export const formOperationConfig = () => [
  {
    label: '运输费率不含税',
    name: 'transportationRateNoTax',
    unit: '元/kg/公里',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '运输距离',
    name: 'transportDistance',
    unit: '公里',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '折现系数',
    name: 'discountRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '残值率',
    name: 'salvageRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 人工成本配置
export const formLaborCostConfig = () => [
  {
    label: '工人数量',
    name: 'workerCount',
    unit: '个',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '工人人均月薪',
    name: 'workerMonthlySalary',
    unit: '元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '技术人员数量',
    name: 'technicianCount',
    unit: '个',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '技术人均月薪',
    name: 'technicianMonthlySalary',
    unit: '元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '管理人员数量',
    name: 'managerCount',
    unit: '个',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '管理人均月薪',
    name: 'managerMonthlySalary',
    unit: '元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '福利社保系数',
    name: 'socialSecurityAndWelfareFactor',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '薪资年增长率',
    name: 'salaryAnnualGrowthRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 土地租赁配置
export const formLandLeaseConfig = () => [
  {
    label: '首年土地租赁费单价',
    name: 'pvLandUnitPrice',
    unit: '元/W',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '土地租赁费增长率',
    name: 'photovoltaicLandRentalFeeGrowthRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '年度土地税费税率',
    name: 'pvLandUnitPriceRate',
    unit: '%',
    type: 'number',
    numberType: 'ratio',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  }
]

// 融资配置
export const formFinancingConfig = () => [
  {
    label: '基础借款利率',
    name: 'baseLoanRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '资本金比例',
    name: 'equityRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '融资比例',
    name: 'financingRatioBase',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '股东分红占比',
    name: 'shareholderDividendRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '法定盈余公积金率',
    name: 'statutorySurplusReserveFundRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 税率配置
export const formTaxConfig = () => [
  {
    label: '维修费增值税率',
    name: 'maintenanceCostVATRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '材料费增值税率',
    name: 'materialsCostVATRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '运输费增值税率',
    name: 'transportationFeeVATRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '水费增值税率',
    name: 'waterFeeVATRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '水资源税单价',
    name: 'waterResourceTaxUnitPrice',
    unit: '元/吨',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '电费增值税率',
    name: 'electricityFeeVATRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '收入项增值税率',
    name: 'revenueItemVATRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '城市建设维护税率',
    name: 'cityConstructionAndMaintenanceTaxRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '教育费附加税率',
    name: 'educationSurchargeRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '地方教育费附加税率',
    name: 'localEducationSurchargeRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '增值税率',
    name: 'VATRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '安装税率',
    name: 'installationTaxRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '所得税率',
    name: 'incomeTaxRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 其他配置（保持向后兼容）
export const formOtherConfig = () => [
  ...formOperationConfig(),
  ...formLaborCostConfig(),
  ...formLandLeaseConfig()
] 