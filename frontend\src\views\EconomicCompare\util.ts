// 定义数据类型接口
interface SolutionData {
  id: string
  solutionName: string
  scenarioType: string
  profitRate: number
  submitTime: string
  status: string
}

interface ProjectData {
  id: string
  projectId: number
  projectName: string
  projectBackground: string
  customerName: string
  solutions: SolutionData[]
}

// 主表格列定义（项目级别）
export const columns = [
  // {
  //   title: '序号',
  //   key: 'sequence',
  //   dataIndex: 'sequence',
  //   width: 60,
  //   align: 'center' as const
  // },
  {
    title: '项目名称',
    dataIndex: 'name',
    key: 'name',
    width: 150
  },
  {
    title: '项目背景',
    dataIndex: 'desc',
    key: 'desc',
    width: 150,
    ellipsis: true
  },
  {
    title: '客户名称',
    dataIndex: 'customer',
    key: 'customer',
    width: 120
  },
  {
    title: '方案列表',
    key: 'solutions',
    align: 'center'
  }
]

// 子表格列定义（方案级别）
export const solutionColumns = [
  {
    title: '方案名称',
    dataIndex: 'name',
    key: 'name',
    width: 140
  },
  {
    title: '场景类型',
    dataIndex: 'topology',
    key: 'topology',
    width: 220
  },
  {
    title: '项目收益率',
    dataIndex: 'metrics',
    key: 'metrics',
    width: 130,
    align: 'center' as const
  },
  {
    title: '提交时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 160
  },
  // {
  //   title: '状态',
  //   dataIndex: 'status',
  //   key: 'status',
  //   width: 80,
  //   align: 'center' as const
  // },
  {
    title: '操作',
    key: 'action',
    width: 150,
    align: 'center' as const
  }
]

// 状态配置
export const statusConfig = {
  processing: { label: '计算中', color: 'processing' },
  success: { label: '成功', color: 'success' },
  failed: { label: '失败', color: 'error' }
}

// 获取状态颜色
export const getStatusColor = (status: string) => {
  return statusConfig[status as keyof typeof statusConfig]?.color || 'default'
}

// 获取状态文本
export const getStatusText = (status: string) => {
  return statusConfig[status as keyof typeof statusConfig]?.label || status
}

// 导出工具函数
export const exportTableData = (selectedData: SolutionData[]) => {
  console.log('导出数据:', selectedData)
  // 这里可以实现实际的导出逻辑
}

// 方案对比函数
export const compareSolutions = (solutions: SolutionData[]) => {
  console.log('对比方案:', solutions)
  // 这里可以实现实际的对比逻辑
}
